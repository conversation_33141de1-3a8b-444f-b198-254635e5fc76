using Microsoft.Bot.ObjectModel;
using System.Numerics;
using System.Reflection.Metadata;
using System.Text;
using System.Text.RegularExpressions;
using System.Globalization;
using Google.Protobuf.WellKnownTypes;
using Newtonsoft.Json;

namespace NDFToCopilotStudioConverter
{
    /// <summary>
    /// <PERSON>ton to access common properties from the Mix project that otherwise need to be calculated. For example, the default channel id.
    /// </summary>
    public class NDFHelper
    {
        private static readonly Lazy<NDFHelper> instance = new Lazy<NDFHelper>(() => new NDFHelper());
        private DialogList _project;
        private String _defaultChannelId;
        private String defaultLanguage;
        private bool _foundIntentMapper = false;
        private NDFHelper()
        {

        }

        private String FormatDate(string date)
        {
            return date.Substring(0, 4) + "-" + date.Substring(4, 2) + "-" + date.Substring(6, 2);
        }

        public static NDFHelper Instance
        {
            get { return instance.Value; }
        }

        /// <summary>
        /// Gets custom display name for specific entity action names
        /// </summary>
        /// <param name="actionName">The action name to get display name for</param>
        /// <returns>Custom display name or the original action name if no custom mapping exists</returns>
        public static string GetCustomDisplayName(string actionName)
        {
            if (string.IsNullOrEmpty(actionName))
                return actionName ?? string.Empty;

            // Add debug logging to see what values we're getting
            Console.WriteLine($"[NDFHelper] GetCustomDisplayName called with: '{actionName}'");

            switch (actionName.ToLower())
            {
                case "MaxNomatch":
                    Console.WriteLine($"[NDFHelper] Matched maxnomatch, returning '???'");
                    return "???";
                case "maxnoinput":
                    Console.WriteLine($"[NDFHelper] Matched maxnoinput, returning '...'");
                    return "...";
                case "clearentity":
                    Console.WriteLine($"[NDFHelper] Matched clearentity, returning '---'");
                    return "---";
                default:
                    Console.WriteLine($"[NDFHelper] No match found for '{actionName}', returning original");
                    return actionName; // Return original name if no custom mapping
            }
        }
    }
}
