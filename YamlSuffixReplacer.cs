﻿using System.Text.RegularExpressions;

namespace NDFToCopilotStudioConverter
{
    public static class YamlSuffixReplacer
    {
        public static string ReplaceYamlSuffix(string yamlContent)
        {
            // Mapping of suffix replacements
            Dictionary<string, string> replacements = new Dictionary<string, string>
        {
            { "_PP", "_MS" },
            { "_DS", "_CD" },
            { "_DM", "_QA" },
            { "_SD", "_RD" },
            { "_XR", "_RD" },
            { "_DB", "_HT" },
            { "_DA", "_HT" },
            { "_SD_return", "_RD_return" },
            { "_XR_return", "_RD_return" }
        };

            // Perform replacements
            foreach (var pair in replacements)
            {
                string pattern = $@"{Regex.Escape(pair.Key)}\b"; // Match only at the end of a word
                yamlContent = Regex.Replace(yamlContent, pattern, pair.Value);
            }

            // Special case: Replace "_DM_" with "_QA_"
            yamlContent = yamlContent.Replace("_DM_", "_QA_");

            return ReplaceYamlInBetween(yamlContent);
        }

        public static string ReplaceYamlInBetween(string yamlContent)
        {
            // Mapping of in-between replacements
            Dictionary<string, string> inBetweenReplacements = new Dictionary<string, string>
        {
            { "_PP_", "_MS_" },
            { "_DS_", "_CD_" },
            { "_DM_", "_QA_" },
            { "_SD_", "_RD_" },
            { "_DB_", "_DA_" }
        };

            // Perform replacements
            foreach (var pair in inBetweenReplacements)
            {
                yamlContent = yamlContent.Replace(pair.Key, pair.Value);
            }

            return yamlContent;
        }
    }


}
