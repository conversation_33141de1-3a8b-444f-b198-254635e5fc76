using Microsoft.Bot.ObjectModel;
using NUnit.Framework;
using System;
using System.ComponentModel.DataAnnotations;
using System.Security.Cryptography.X509Certificates;
using System.Text;
using System.Text.RegularExpressions;
using DataType = Microsoft.Bot.ObjectModel.DataType;
using Microsoft.PS.Tools;

//rename namespace from MixToCopilotStudioConverter to NDFToCopilotStudioConverter
namespace NDFToCopilotStudioConverter
{
    public class ComponentConverter
    {
        private DialogModel _component;
        private Guid _botId;
        private List<CustomStateModel> _customStates;
        private Dictionary<string, CopilotStudioEntity> _externalGrammarToEntityMap;
        private Dictionary<string, string> grammarNameToEntityMap = new Dictionary<string, string>();
        private FeatureFlagOptions _featureFlags;

        private const string INITIAL_PROMPT_TEXT_VARIABLE_NAME = "Global.initialPromptText";
        private const string INITIAL_PROMPT_SPEECH_VARIABLE_NAME = "Global.initialPromptSpeech";
        private const string NOINPUT_PROMPT_TEXT_VARIABLE_NAME = "Global.NoInputPromptText";
        private const string NOINPUT_PROMPT_SPEECH_VARIABLE_NAME = "Global.NoInputPromptSpeech";
        private const string NOMATCH_PROMPT_TEXT_VARIABLE_NAME = "Global.NoMatchPromptText";
        private const string NOMATCH_PROMPT_SPEECH_VARIABLE_NAME = "Global.NoMatchPromptSpeech";
        private const string API_BASE_URL = "https://daframeworkwest-h7aredefabatf6eh.canadacentral-01.azurewebsites.net/";
        private const string CREATE_SESSION_URL = "https://daframeworkwest-h7aredefabatf6eh.canadacentral-01.azurewebsites.net/session/create";

        public ComponentConverter(DialogModel component, Guid botId, List<CustomStateModel> externalCustomStates, FeatureFlagOptions featureFlagOptions)
        {
            _component = component;
            _botId = botId;
            _customStates = externalCustomStates;
            _externalGrammarToEntityMap = new Dictionary<string, CopilotStudioEntity>();
            _featureFlags = featureFlagOptions;
        }

        // Method to get the populated grammar name to entity map
        public Dictionary<string, string> GetGrammarNameToEntityMap()
        {
            return grammarNameToEntityMap;
        }

        public DialogComponent ConvertToDialogComponent()
        {
            DialogComponent.Builder dialogComponentBuilder = new DialogComponent.Builder();
            dialogComponentBuilder.DisplayName = _component.dialogName;
            dialogComponentBuilder.ParentBotId = _botId;
            dialogComponentBuilder.SchemaName = new DialogSchemaName("topic." + _component.dialogName);
            BeginDialog.Builder beginDialog = new BeginDialog().ToBuilder();

            TriggerBase.Builder triggerBase;
            /*Console.WriteLine(_component.dialogName);*/
            if (_component.dialogName.Equals("ConversationStart", StringComparison.OrdinalIgnoreCase))
            {
                triggerBase = new OnConversationStart().ToBuilder();
            }
            else
            {
                triggerBase = new OnRedirect().ToBuilder();
            }

            triggerBase.Id = "main";//_component.dialogName;

            // Convert session mappings
            processSessionMapping(triggerBase, _component.SessionMappings, "");

            if (_component.dialogName.Equals("ApplicationRoot", StringComparison.OrdinalIgnoreCase))
            {
                BeginDialog.Builder redirectDialog = new BeginDialog.Builder()
                {
                    Id = ObjectModelHelper.GenerateRandomID(),
                    Dialog = DialogExpression.Literal("topic.Initialization")
                };
                triggerBase.Actions.Actions.Add(redirectDialog);
            }
            else
            {
                if (!_component.dialogName.Equals("ConversationStart", StringComparison.OrdinalIgnoreCase)
                    && !_component.dialogName.StartsWith("declareVariables_")
                    && !_component.dialogName.Equals("BackendInitialization", StringComparison.OrdinalIgnoreCase)
                    && _component.States.Count >= 1)
                {
                    var FirstStateId = "";
                    LogCustomTelemetryEvent logCustomEvent = new LogCustomTelemetryEvent("checkTransition");
                    LogCustomTelemetryEvent.Builder startTelemetry = BuildLogTelemetryEvent("checkTransition_LG");
                    triggerBase.Actions.Add(startTelemetry);

                    var conditionGroupBuilder = new ConditionGroup.Builder
                    {
                        Id = "checkTransition_tv",
                        DisplayName = _component.dialogName + "_NextState_CG"
                    };

                    foreach (var possibleState in _component.States)
                    {
                        if (FirstStateId == "")
                        {
                            FirstStateId = possibleState.Id;
                        }
                        string possibleStateId = ObjectModelHelper.getLoggingStateName(possibleState.Id, "LG");
                        // Create a condition item builder
                        var conditionItemBuilder = new ConditionItem.Builder
                        {
                            Id = "condItem_getNextState_CD_" + ObjectModelHelper.GenerateRandomID(),
                            Condition = "Global.nextState = \"" + possibleStateId + "\""
                        };
                        var gotoAction = new GotoAction.Builder()
                        {
                            Id = ObjectModelHelper.GenerateRandomID(),
                            ActionId = possibleStateId
                        };
                        conditionItemBuilder.Actions.Add(ObjectModelHelper.SetVariableToBlank("Global.nextState"));

                        conditionItemBuilder.Actions.Add(gotoAction);

                        conditionGroupBuilder.Conditions.Add(conditionItemBuilder.Build());
                    }

                    var elseAction = new GotoAction.Builder()
                    {
                        Id = ObjectModelHelper.GenerateRandomID(),
                        ActionId = ObjectModelHelper.getLoggingStateName(FirstStateId, "LG")
                    };
                    conditionGroupBuilder.ElseActions.Add(elseAction);
                    triggerBase.Actions.Add(conditionGroupBuilder.Build());

                }

                foreach (var node in _component.States)
                {
                    if (node.IsStartNode)
                    {
                        ConvertNode(triggerBase, node, _featureFlags);
                    }
                    else
                    {
                        ConvertNode(triggerBase, node, _featureFlags);
                    }
                }
            }
            EndDialog.Builder endDialogBuilder = new EndDialog.Builder()
            {
                Id = "return"
            };
            triggerBase.Actions.Add(endDialogBuilder);


            AdaptiveDialog.Builder adaptiveDialog = new AdaptiveDialog.Builder();
            adaptiveDialog.BeginDialog = triggerBase;

            dialogComponentBuilder.Dialog = adaptiveDialog.Build();

            return dialogComponentBuilder.Build();
        }


        private void ConvertNode(TriggerBase.Builder triggerBase, StateModel node, FeatureFlagOptions featureFlags)
        {
            String externalCustomStateString = CreateConcatenatedEventNameString(_customStates);
            /* Console.WriteLine("------------------------------external custom state string is "+externalCustomStateString+"------------------------------------------");
             Console.WriteLine("Processing node: " + node.Id);
 */
            if (node is PlayStateModel playStateNode)
            {
                ConvertMessageNode(triggerBase, playStateNode);
            }
            else if (node is DecisionStateModel decisionStateNode)
            {
                ConvertDecisionStateNode(triggerBase, decisionStateNode);
            }
            else if (node is CustomStateModel customStateNode)
            {
                ConvertCustomStateNode(triggerBase, customStateNode);
            }
            else if (node is SubdialogStateModel subdialogStateModel)
            {
                ConvertSubdialogStateNode(triggerBase, subdialogStateModel);
            }
            else if (node is DmStateModel dmStateModel)
            {
                if (featureFlags.IsPowerFxQA) // generate QA node with powerfx
                {
                    convertQuestionNodeWithPowerFx(triggerBase, dmStateModel);
                }
                else
                {

                    convertQuestionNode(triggerBase, dmStateModel);
                }
            }
            else if (node is DataAccessModel dataAccessModel)
            {
                if (featureFlags.IsPlaceholderDA) // generate placeholder DA node
                {
                    convertToPlaceHolderDataAccessNode(triggerBase, dataAccessModel);
                }
                else
                {
                    convertDataAccessNode(triggerBase, dataAccessModel);
                }
            }
        }

        public static string CreateConcatenatedEventNameString(List<CustomStateModel> customStateModels)
        {
            var eventNames = new List<string>();

            foreach (var customState in customStateModels)
            {
                /*foreach (var conditionList in customState.ConditionTransitionsList)
                {
                    foreach (var condition in conditionList)
                    {
                        if (condition.eventCalls != null && !string.IsNullOrEmpty(condition.eventCalls.Name))
                        {
                            eventNames.Add(condition.eventCalls.Name);
                        }
                    }
                }*/
                eventNames.Add(customState.Id);
            }

            // Join all event names with an underscore (_)
            return string.Join("_", eventNames);
        }


        private void ConvertSubdialogStateNodeForCustomNode(TriggerBase.Builder triggerBase, CustomStateModel subdialogStateModel)
        {

            if (subdialogStateModel != null)
            {
                string dialogName = "";
                if (subdialogStateModel.Id.Contains("_"))
                {
                    dialogName = subdialogStateModel.Id.Split('_')[1];
                }

                // BeginDialog.Builder redirectConversation = new BeginDialog.Builder()
                // {
                //     Id = ObjectModelHelper.GenerateRandomID(),
                //     DisplayName = subdialogStateModel.Id,
                //     Dialog = DialogExpression.Literal("topic." + dialogName)

                // };

                var gotoAction = new GotoAction.Builder
                {

                    Id = ObjectModelHelper.GenerateRandomID(),

                    ActionId = dialogName

                };

                triggerBase.Actions.Add(gotoAction);

                DialogComponent.Builder dialogComponentBuilder = new DialogComponent.Builder();
                dialogComponentBuilder.DisplayName = dialogName;
                dialogComponentBuilder.SchemaName = new DialogSchemaName("topic." + dialogName);
                BeginDialog.Builder beginDialog = new BeginDialog().ToBuilder();

                triggerBase = new OnSystemRedirect();
            }
        }


        private void ConvertSubdialogStateNode(TriggerBase.Builder triggerBase, SubdialogStateModel subdialogStateModel)
        {
            string logStateName = ObjectModelHelper.getLoggingStateName(subdialogStateModel.Id, "LG");
            LogCustomTelemetryEvent.Builder startTelemetry = BuildLogTelemetryEvent(logStateName);
            triggerBase.Actions.Add(startTelemetry);
            processSessionMapping(triggerBase, subdialogStateModel.SessionMappings, subdialogStateModel.Id);
            if (subdialogStateModel != null)
            {
                if (subdialogStateModel.gotodialog != null && subdialogStateModel.gotodialog.EndsWith(".dvxml"))
                {
                    subdialogStateModel.gotodialog = subdialogStateModel.gotodialog.Replace(".dvxml", "");
                }
                BeginDialog.Builder redirectConversation = new BeginDialog.Builder()
                {
                    Id = ObjectModelHelper.GenerateRandomID(),
                    Dialog = DialogExpression.Literal("topic." + subdialogStateModel.gotodialog)

                };

                triggerBase.Actions.Add(redirectConversation);

                ConditionGroup.Builder conditionGroupBuilder = new ConditionGroup.Builder()
                {
                    Id = subdialogStateModel.Id + ObjectModelHelper.GenerateRandomID(),
                    DisplayName = ObjectModelHelper.getLoggingStateName(subdialogStateModel.Id, "CG")
                };
                // Create a condition item builder
                ConditionItem.Builder conditionItemBuilder = new ConditionItem.Builder()
                {
                    Id = "condItem_" + ObjectModelHelper.getLoggingStateName(subdialogStateModel.Id, "CD") + "_" + ObjectModelHelper.GenerateRandomID(),
                    Condition = StateUtility.TransformCondition("=true")
                };
                String externalCustomStateString = CreateConcatenatedEventNameString(_customStates);

                if (subdialogStateModel.ConditionTransitionsList.Count == 1)
                {
                    processSessionMapping(triggerBase, subdialogStateModel.SessionMappings, subdialogStateModel.Id);
                    foreach (var conditionTransitions in subdialogStateModel.ConditionTransitionsList)
                    {
                        foreach (var conditionModel in conditionTransitions)
                        {
                            if (conditionModel.Condition == "default")
                            {
                                if (!string.IsNullOrEmpty(conditionModel.Transitions.next))
                                {
                                    string gotoNextNode = conditionModel.Transitions.next;
                                    if (externalCustomStateString.Contains(gotoNextNode))
                                    {
                                        BeginDialog.Builder redirectConversationNext = new BeginDialog.Builder()
                                        {
                                            Id = ObjectModelHelper.GenerateRandomID(),
                                            Dialog = DialogExpression.Literal("topic." + gotoNextNode)

                                        };
                                        triggerBase.Actions.Add(redirectConversationNext);
                                    }
                                    if (gotoNextNode.EndsWith(".dvxml"))
                                    {
                                        gotoNextNode = gotoNextNode.Replace(".dvxml", "");
                                        BeginDialog.Builder redirectDialog = new BeginDialog.Builder()
                                        {
                                            Id = ObjectModelHelper.GenerateRandomID(),
                                            Dialog = DialogExpression.Literal("topic." + gotoNextNode)
                                        };
                                        triggerBase.Actions.Add(redirectDialog);

                                    }

                                }
                            }
                        }
                    }
                }
                else
                {
                    foreach (var sessionMapping in subdialogStateModel.SessionMappings)
                    {
                        if (string.IsNullOrEmpty(sessionMapping.value))
                        {
                            conditionItemBuilder.Actions.Add(ObjectModelHelper.SetVariableToBlank("Global." + sessionMapping.key));
                        }
                        else
                        {
                            conditionItemBuilder.Actions.Add(ObjectModelHelper.SetVariable("Global." + sessionMapping.key, sessionMapping.value, sessionMapping.type, subdialogStateModel.Id));
                        }
                    }

                    bool isDefault = false;
                    // Process condition transitions
                    if (subdialogStateModel.ConditionTransitionsList.Count > 1)
                    {
                        foreach (var conditionTransitions in subdialogStateModel.ConditionTransitionsList)
                        {
                            foreach (var conditionModel in conditionTransitions)
                            {
                                if (conditionModel.Condition == "default")
                                {
                                    isDefault = true;
                                    //processSessionMapping(triggerBase, conditionModel.Transitions.sessionMappings);

                                    foreach (var sessionMapping in conditionModel.Transitions.sessionMappings)
                                    {
                                        if (string.IsNullOrEmpty(sessionMapping.value))
                                        {
                                            conditionItemBuilder.Actions.Add(ObjectModelHelper.SetVariableToBlank("Global." + sessionMapping.key));
                                        }
                                        else
                                        {
                                            conditionItemBuilder.Actions.Add(ObjectModelHelper.SetVariable("Global." + sessionMapping.key, sessionMapping.value, sessionMapping.type, subdialogStateModel.Id));
                                        }
                                    }

                                    if (!string.IsNullOrEmpty(conditionModel.Transitions.next))
                                    {
                                        string gotoNextNode = conditionModel.Transitions.next;
                                        if (externalCustomStateString.Contains(gotoNextNode))
                                        {
                                            BeginDialog.Builder redirectConversationNext = new BeginDialog.Builder()
                                            {
                                                Id = ObjectModelHelper.GenerateRandomID(),
                                                Dialog = DialogExpression.Literal("topic." + gotoNextNode)

                                            };
                                            conditionItemBuilder.Actions.Add(redirectConversationNext);
                                        }
                                        else
                                        {
                                            gotoNextNode = getNextNodeForCondition(conditionItemBuilder, gotoNextNode);

                                        }
                                    }
                                }
                                else
                                {
                                    // Process and add actions to both the condition group and the condition item builder
                                    ProcessConditionTransitions(conditionGroupBuilder, conditionModel, triggerBase, subdialogStateModel.Id);

                                }

                            }
                            if (isDefault == false)
                            {
                                conditionItemBuilder.Actions.Add(conditionGroupBuilder.Build());
                            }

                        }
                    }

                    //Adding beignDialog method for goto that are outside action
                    if (!string.IsNullOrEmpty(subdialogStateModel.gotodialog))
                    {
                        string gotodialogElement = subdialogStateModel.gotodialog;
                        BeginDialog.Builder redirectConversationNext = new BeginDialog.Builder()
                        {
                            Id = ObjectModelHelper.GenerateRandomID(),
                            Dialog = DialogExpression.Literal("topic." + gotodialogElement)

                        };
                        conditionItemBuilder.Actions.Add(redirectConversationNext);

                    }

                    // Add the condition item to the condition group
                    if (isDefault == true)
                    {
                        conditionGroupBuilder.Conditions.Add(conditionItemBuilder.Build());
                    }

                    // Add the condition group to the trigger base actions
                    if (conditionGroupBuilder.Conditions.Count > 0)
                    {
                        triggerBase.Actions.Add(conditionGroupBuilder.Build());
                    }
                }
            }


        }

        private void ConvertCustomStateNode(TriggerBase.Builder triggerBase, CustomStateModel customStateNode)
        {
            string logStateName = ObjectModelHelper.getLoggingStateName(customStateNode.Id, "LG");
            LogCustomTelemetryEvent.Builder startTelemetry = BuildLogTelemetryEvent(logStateName);

            triggerBase.Actions.Add(startTelemetry);

            processSessionMapping(triggerBase, customStateNode.SessionMappings, customStateNode.Id);

            Boolean isRegularCustomState = false;
            if (customStateNode != null && (customStateNode.Id.EndsWith("_SD") || customStateNode.Id.EndsWith("_XR")))
            {
                isRegularCustomState = true;
                ConvertSubdialogStateNodeForCustomNode(triggerBase, customStateNode);
            }
            else if (customStateNode != null && customStateNode.Id == "end")
            {
                isRegularCustomState = true;
                EndConversation.Builder endConversation = new EndConversation.Builder()
                {
                    Id = customStateNode.Id,
                    DisplayName = customStateNode.Id
                };
                triggerBase.Actions.Add(endConversation);
            }
            else if (customStateNode != null && customStateNode.ConditionTransitionsList != null && customStateNode.ConditionTransitionsList.Count >= 1)
            {
                if (customStateNode.ConditionTransitionsList[0].Count == 1)
                {
                    var transitions = customStateNode.ConditionTransitionsList[0];
                    var model = transitions[0];
                    if (model != null && model.Transitions.next != null)
                    {
                        if (model.Transitions.next.EndsWith("_SD") || model.Transitions.next.EndsWith("_XR"))
                        {
                            isRegularCustomState = true;
                            String dialogName = model.Transitions.next;

                            // BeginDialog.Builder redirectConversation = new BeginDialog.Builder()
                            // {
                            //     //changing the id to dispaly name
                            //     Id = ObjectModelHelper.GenerateRandomID(),
                            //     DisplayName = customStateNode.Id,
                            //     Dialog = DialogExpression.Literal("topic." + dialogName)

                            // };

                            // triggerBase.Actions.Add(redirectConversation);

                            //changing the above code to use GotoAction instead of BeginDialog for transition
                            var gotoAction = new GotoAction.Builder
                            {
                                //changing the id to dispaly name
                                Id = ObjectModelHelper.GenerateRandomID(),
                                DisplayName = customStateNode.Id,
                                //Dialog = DialogExpression.Literal("topic." + dialogName)
                                ActionId = ObjectModelHelper.getLoggingStateName(dialogName, "LG")
                            };

                            triggerBase.Actions.Add(gotoAction);

                            DialogComponent.Builder dialogComponentBuilder = new DialogComponent.Builder();
                            dialogComponentBuilder.DisplayName = dialogName;
                            dialogComponentBuilder.SchemaName = new DialogSchemaName("topic." + dialogName);
                            BeginDialog.Builder beginDialog = new BeginDialog().ToBuilder();

                            triggerBase = new OnSystemRedirect();
                        }
                    }
                }
            }

            if (isRegularCustomState == false)
            {
                ConditionGroup.Builder conditionGroupBuilder = new ConditionGroup.Builder()
                {
                    Id = customStateNode.Id,
                    DisplayName = ObjectModelHelper.getLoggingStateName(customStateNode.Id, "CG")
                };

                // Create a condition item builder
                ConditionItem.Builder conditionItemBuilder = new ConditionItem.Builder()
                {
                    Id = "condItem_" + ObjectModelHelper.getLoggingStateName(customStateNode.Id, "CD") + "_" + ObjectModelHelper.GenerateRandomID(),
                    Condition = StateUtility.TransformCondition("=true")
                };
                String externalCustomStateString = CreateConcatenatedEventNameString(_customStates);

                bool isDefault = false;
                if (customStateNode.ConditionTransitionsList.Count > 0 &&
                    customStateNode.ConditionTransitionsList[0].Count == 1)
                {
                    foreach (var conditionTransitions in customStateNode.ConditionTransitionsList)
                    {
                        foreach (var conditionModel in conditionTransitions)
                        {
                            foreach (var sessionMapping in conditionModel.Transitions.sessionMappings)
                            {
                                if (string.IsNullOrEmpty(sessionMapping.value))
                                {
                                    triggerBase.Actions.Add(ObjectModelHelper.SetVariableToBlank("Global." + sessionMapping.key));
                                }
                                else
                                {
                                    triggerBase.Actions.Add(ObjectModelHelper.SetVariable("Global." + sessionMapping.key, sessionMapping.value, sessionMapping.type, customStateNode.Id));
                                }
                            }

                            if (!string.IsNullOrEmpty(conditionModel.Transitions.next))
                            {
                                string gotoNextNode = conditionModel.Transitions.next;

                                if (externalCustomStateString.Contains(gotoNextNode))
                                {
                                    BeginDialog.Builder redirectConversation = new BeginDialog.Builder()
                                    {
                                        Id = ObjectModelHelper.GenerateRandomID(),
                                        Dialog = DialogExpression.Literal("topic." + gotoNextNode)

                                    };
                                    triggerBase.Actions.Add(redirectConversation);
                                }
                                else
                                {
                                    gotoNextNode = getNextNodeForNoCondition(triggerBase, gotoNextNode);

                                }
                            }

                        }
                    }
                }
                else
                {
                    // Process condition transitions
                    if (customStateNode.ConditionTransitionsList.Count > 0)
                    {
                        foreach (var conditionTransitions in customStateNode.ConditionTransitionsList)
                        {
                            foreach (var conditionModel in conditionTransitions)
                            {
                                if (conditionModel.Condition == "default")
                                {
                                    isDefault = true;
                                    // String externalCustomStateString = CreateConcatenatedEventNameString(_customStates);
                                    //processSessionMapping(triggerBase, conditionModel.Transitions.sessionMappings);
                                    foreach (var sessionMapping in conditionModel.Transitions.sessionMappings)
                                    {
                                        if (string.IsNullOrEmpty(sessionMapping.value))
                                        {
                                            conditionItemBuilder.Actions.Add(ObjectModelHelper.SetVariableToBlank("Global." + sessionMapping.key));
                                        }
                                        else
                                        {
                                            conditionItemBuilder.Actions.Add(ObjectModelHelper.SetVariable("Global." + sessionMapping.key, sessionMapping.value, sessionMapping.type, customStateNode.Id));
                                        }
                                    }

                                    if (!string.IsNullOrEmpty(conditionModel.Transitions.next))
                                    {
                                        string gotoNextNode = conditionModel.Transitions.next;

                                        if (externalCustomStateString.Contains(gotoNextNode))
                                        {
                                            BeginDialog.Builder redirectConversation = new BeginDialog.Builder()
                                            {
                                                Id = ObjectModelHelper.GenerateRandomID(),
                                                Dialog = DialogExpression.Literal("topic." + gotoNextNode)

                                            };
                                            conditionItemBuilder.Actions.Add(redirectConversation);
                                        }
                                        else
                                        {
                                            gotoNextNode = getNextNodeForCondition(conditionItemBuilder, gotoNextNode);

                                        }
                                    }
                                }
                                else
                                {
                                    // Process and add actions to both the condition group and the condition item builder
                                    ProcessConditionTransitions(conditionGroupBuilder, conditionModel, triggerBase, customStateNode.Id);

                                }

                            }
                            if (isDefault == false)
                            {
                                conditionItemBuilder.Actions.Add(conditionGroupBuilder.Build());
                            }

                        }
                    }

                    // Add the condition item to the condition group
                    if (isDefault == true)
                    {
                        conditionGroupBuilder.Conditions.Add(conditionItemBuilder.Build());
                    }

                    // Add the condition group to the trigger base actions
                    if (conditionGroupBuilder.Conditions.Count > 0)
                    {
                        triggerBase.Actions.Add(conditionGroupBuilder.Build());
                    }
                }


            }
        }

        private void ConvertMessageNode(TriggerBase.Builder beginDialog, PlayStateModel playStateNode)
        {
            string logStateName = ObjectModelHelper.getLoggingStateName(playStateNode.Id, "LG");
            LogCustomTelemetryEvent.Builder startTelemetry = BuildLogTelemetryEvent(logStateName);
            beginDialog.Actions.Add(startTelemetry);
            // Convert session mappings
            processSessionMapping(beginDialog, playStateNode.SessionMappings, playStateNode.Id);

            // Initialize the top-level condition group builder
            if (playStateNode.ConditionPromptList.Count > 0)
            {
                // var conditionGroupBuilder = new ConditionGroup.Builder
                // {
                //     Id = playStateNode.Id+"_tv",
                //     DisplayName = ObjectModelHelper.getLoggingStateName(playStateNode.Id,"CG")
                // };
                foreach (var conditionPrompts in playStateNode.ConditionPromptList)
                {
                    var conditionGroupBuilder = new ConditionGroup.Builder
                    {
                        Id = playStateNode.Id + "_" + ObjectModelHelper.GenerateRandomID() + "_tv",
                        DisplayName = ObjectModelHelper.getLoggingStateName(playStateNode.Id, "CG")
                    };
                    foreach (var conditionModel in conditionPrompts)
                    {
                        ProcessConditionTransitions(conditionGroupBuilder, conditionModel, beginDialog, playStateNode.Id);
                    }
                    if (conditionGroupBuilder.Conditions.Count > 0)
                    {
                        beginDialog.Actions.Add(conditionGroupBuilder.Build());
                    }
                }
                // if (conditionGroupBuilder.Conditions.Count > 0)
                // {
                //     beginDialog.Actions.Add(conditionGroupBuilder.Build());
                // }

            }

            if (playStateNode.ConditionTransitionsList.Count > 0)
            {
                string id = playStateNode.Id + "_" + ObjectModelHelper.GenerateRandomID();
                var conditionGroupBuilder = new ConditionGroup.Builder
                {
                    Id = id + "_tv",
                    DisplayName = ObjectModelHelper.getLoggingStateName(playStateNode.Id, "CG")
                };
                // Process each list of condition transitions (separate if-blocks)
                foreach (var conditionTransitions in playStateNode.ConditionTransitionsList)
                {
                    foreach (var conditionModel in conditionTransitions)
                    {
                        ProcessConditionTransitions(conditionGroupBuilder, conditionModel, beginDialog, playStateNode.Id);
                    }
                }
                // Add the condition group to the trigger base actions
                if (conditionGroupBuilder.Conditions.Count > 0)
                {

                    beginDialog.Actions.Add(conditionGroupBuilder.Build());
                }

            }
            if (playStateNode.gotoDialogElement != null)
            {
                string gotoNextNode = playStateNode.gotoDialogElement;
                if (gotoNextNode.EndsWith(".dvxml"))
                {
                    gotoNextNode = gotoNextNode.Replace(".dvxml", "");
                }
                BeginDialog.Builder redirectConversation = new BeginDialog.Builder()
                {
                    Id = ObjectModelHelper.GenerateRandomID(),
                    Dialog = DialogExpression.Literal("topic." + gotoNextNode)

                };
                beginDialog.Actions.Add(redirectConversation);
            }
        }

        private void ConvertDecisionStateNode(TriggerBase.Builder triggerBase, DecisionStateModel decisionStateNode)
        {
            if (decisionStateNode.label == true)
            {
                DecisionStateDANode(triggerBase, decisionStateNode);
            }
            else
            {
                LogCustomTelemetryEvent logCustomEvent = new LogCustomTelemetryEvent(decisionStateNode.Id);
                string logStateName = ObjectModelHelper.getLoggingStateName(decisionStateNode.Id, "LG");
                var logCustomEventDA = new LogCustomTelemetryEvent.Builder()
                {
                    Id = logStateName,
                    DisplayName = logStateName,
                    EventName = logStateName,
                };
                triggerBase.Actions.Add(logCustomEventDA);
            }

            // Convert session mappings
            processSessionMapping(triggerBase, decisionStateNode.SessionMappings, decisionStateNode.Id);

            // Initialize the top-level condition group builder
            if (decisionStateNode.ConditionTransitionsList.Count > 0)
            {
                // var conditionGroupBuilder = new ConditionGroup.Builder
                // {
                //     Id = decisionStateNode.Id+ "_tv",
                //     DisplayName = ObjectModelHelper.getLoggingStateName(decisionStateNode.Id , "CG")
                // };

                // Process each list of condition transitions (separate if-blocks)
                foreach (var conditionTransitions in decisionStateNode.ConditionTransitionsList)
                {
                    var conditionGroupBuilder = new ConditionGroup.Builder
                    {
                        Id = decisionStateNode.Id + "_" + ObjectModelHelper.GenerateRandomID() + "_tv",
                        DisplayName = ObjectModelHelper.getLoggingStateName(decisionStateNode.Id, "CG")
                    };
                    foreach (var conditionModel in conditionTransitions)
                    {
                        ProcessConditionTransitions(conditionGroupBuilder, conditionModel, triggerBase, decisionStateNode.Id);
                    }
                    // Add the condition group to the trigger base actions
                    if (conditionGroupBuilder.Conditions.Count > 0)
                    {
                        triggerBase.Actions.Add(conditionGroupBuilder.Build());
                    }
                }

                // Add the condition group to the trigger base actions
                // if (conditionGroupBuilder.Conditions.Count > 0)
                // {
                //     triggerBase.Actions.Add(conditionGroupBuilder.Build());
                // }
            }
            //Adding code for lastgot
            if (decisionStateNode.nextGoto != null)
            {
                if (decisionStateNode.nextGoto.EndsWith(".dvxml"))
                {
                    decisionStateNode.nextGoto = decisionStateNode.nextGoto.Replace(".dvxml", "");
                }
                BeginDialog.Builder redirectDialog = new BeginDialog.Builder()
                {
                    Id = ObjectModelHelper.GenerateRandomID(),
                    Dialog = DialogExpression.Literal("topic." + decisionStateNode.nextGoto)
                };
                triggerBase.Actions.Add(redirectDialog);
            }
        }

        private void DecisionStateDANode(TriggerBase.Builder beginDialog, DecisionStateModel decisionStateModel)
        {
            string mcsNodeId = decisionStateModel.Id + "_" + "00";
            var methodType = HttpMethodTypeWrapper.Get(HttpMethodType.Post);
            StringBuilder body = new StringBuilder(); // for server side

            // For outputs, define the output type
            ActionOutputBinding.Builder actionOutputBinding = new ActionOutputBinding.Builder();
            StringBuilder outputJSON = new StringBuilder();
            StringBuilder logOutput = new StringBuilder();
            string label = "label"; // Assuming this is your output variable
            outputJSON.Append("{\n");
            outputJSON.Append("\"returnCode\": \"0\",\n");

            logOutput.Append("={\n");


            logOutput.Append("\t" + "output_" + mcsNodeId + "_" + label + ": " + "Global." + label);


            logOutput.Append("}");
            // Since you only have one output variable named "label"


            // Create a JSON entry for the label variable
            string variableJSON = $"\"{label}\": \"{label}\""; // Replace with the actual value if needed
            outputJSON.Append(variableJSON);

            // Bind the label output variable
            actionOutputBinding.Binding.Add(label, InitializablePropertyPath.Create("Global." + label));

            // Close the JSON object
            outputJSON.Append("\n}");

            string finalJSON = outputJSON.ToString();
            // Console.WriteLine("Generated JSON: " + finalJSON);

            DataType outputDataType = ObjectModelHelper.GetDataTypeFromJSON(finalJSON);




            // New code: build the httpRequestAction with the structure like the YAML example
            string outputVariableName = "Global." + mcsNodeId;
            HttpRequestAction.Builder httpRequestAction = new HttpRequestAction.Builder()
            {
                Id = mcsNodeId,
                DisplayName = mcsNodeId,
                Method = methodType,
                //Url = "Global.APIbaseUrl" + decisionStateModel.Id,
                //StringExpression.Expression("=Concatenate(" + daNode.baseUrl + "\"," + daNode.dataaccessId + "\")"),
                Url = StringExpression.Expression("=Concatenate(Global.APIbaseUrl,\"" + decisionStateModel.Id + "\")"),
                Response = InitializablePropertyPath.Create("Global." + mcsNodeId),
                //ResponseSchema = outputDataType,
                Body = new JsonRequestContent.Builder()
                {
                    Content = ValueExpression.Expression(body.ToString())
                }
            };

            httpRequestAction.RequestTimeoutInMilliseconds = 30000;
            httpRequestAction.Headers.Add("Content-Type", "application/json");
            httpRequestAction.Headers.Add("Authorization", "Basic YWRtaW46YWRtaW4xMjM=");
            httpRequestAction.Headers.Add("Cookie", "=Concatenate(\"SESSION=\",Global.backendSessionId)");
            httpRequestAction.Headers.Add("password", "=Global.password");
            httpRequestAction.Headers.Add("username", "=Global.username");

            // Adding latency message settings
            /*httpRequestAction.LatencyMessageSettings = new LatencyMessageSettings.Builder()
            {
                AllowLatencyMessage = true,
                LatencyMessageDelayInMilliseconds = 501,
                MinimumPlaybackInMilliseconds = 501
            };*/

            beginDialog.Actions.Add(httpRequestAction);

            beginDialog.Actions.Add(ObjectModelHelper.SetVariableToExpression("Global.label", "Global." + mcsNodeId + "." + "label", mcsNodeId));

            LogCustomTelemetryEvent.Builder outputTelemetry = new LogCustomTelemetryEvent.Builder()
            {
                Id = "DSDAOutputs_" + decisionStateModel.Id,
                DisplayName = "DSDAOutputs_" + decisionStateModel.Id,
                EventName = "DecisionStateDAOutput",
                Properties = ValueExpression.Expression(logOutput.ToString())
            };
            beginDialog.Actions.Add(outputTelemetry);

            var gotoAction = new GotoAction.Builder()
            {
                Id = ObjectModelHelper.GenerateRandomID(),
                ActionId = decisionStateModel.Id
            };
            beginDialog.Actions.Add(gotoAction);
        }



        private void convertQuestionNode(TriggerBase.Builder beginDialog, DmStateModel questionNode)
        {
            string logStateName = ObjectModelHelper.getLoggingStateName(questionNode.Id, "LG");
            LogCustomTelemetryEvent.Builder startTelemetry = BuildLogTelemetryEvent(logStateName);
            beginDialog.Actions.Add(startTelemetry);
            processSessionMapping(beginDialog, questionNode.SessionMappings, questionNode.Id);

            Question.Builder question = new Question.Builder();
            question.Id = questionNode.Id;
            question.DisplayName = questionNode.Id;
            if (questionNode.CollectionConfiguration != null)
            {
                if (questionNode.CollectionConfiguration.PromptConfiguration.InitialPrompt != null)
                {
                    //ConvertPlayStateModelForQuestionNodeEntry(
                    //       questionNode.Id,
                    //       INITIAL_PROMPT_TEXT_VARIABLE_NAME,
                    //       INITIAL_PROMPT_SPEECH_VARIABLE_NAME,
                    //       beginDialog,
                    //       questionNode.CollectionConfiguration.PromptConfiguration.InitialPrompt, "_InitialPrompt", 0, 0
                    //   );
                    if (questionNode.CollectionConfiguration.PromptConfiguration.InitialPrompt.ConditionPromptList.Count > 0)
                    {
                        //LogCustomTelemetryEvent.Builder promptSetTelemetry = new LogCustomTelemetryEvent.Builder()
                        //{
                        //    Id = questionNode.Id ,
                        //    DisplayName = questionNode.Id,
                        //    EventName = questionNode.Id 
                        //};
                        //beginDialog.Actions.Add(promptSetTelemetry);

                        string id = questionNode.Id;//+ "_CD_01";
                        var conditionGroupBuilder = new ConditionGroup.Builder
                        {
                            Id = id + "_" + ObjectModelHelper.GenerateRandomID(),
                            DisplayName = id
                        };
                        // Process each list of condition transitions (separate if-blocks)

                        foreach (var conditionTransitions in questionNode.CollectionConfiguration.PromptConfiguration.InitialPrompt.ConditionPromptList)
                        {
                            foreach (var conditionModel in conditionTransitions)
                            {
                                ProcessConditionTransitionsRetryPrompts(conditionGroupBuilder, conditionModel, beginDialog, "_CD_02", INITIAL_PROMPT_TEXT_VARIABLE_NAME, INITIAL_PROMPT_SPEECH_VARIABLE_NAME, questionNode.Id, 0);
                            }
                        }
                        // Add the condition group to the trigger base actions
                        if (conditionGroupBuilder.Conditions.Count > 0)
                        {

                            beginDialog.Actions.Add(conditionGroupBuilder.Build());
                        }
                    }
                }
                //question.DefaultValue = new StringDataValue("MaxNomatch");//"Text(\"MaxNomatch\")";

                // String bargeInOverride = getNodeSettingOverride("bargein", questionNode.NodeSettingOverride);
                InterruptionPolicy.Builder interruptionPolicy = new InterruptionPolicy.Builder();
                interruptionPolicy.AllowInterruption = true;
                /*if (bargeInOverride != "")
                {
                    interruptionPolicy.AllowInterruption = false;
                }
                else
                {
                    interruptionPolicy.AllowInterruption = true;
                }*/
                question.InterruptionPolicy = interruptionPolicy;
                question.Prompt = ObjectModelHelper.GetSingleLineActivityWithTextAndSpeech(INITIAL_PROMPT_TEXT_VARIABLE_NAME, INITIAL_PROMPT_SPEECH_VARIABLE_NAME);
                question.AlwaysPrompt = true;
                int retryPromptCount = 1;
                int noInputListSize = questionNode.CollectionConfiguration.PromptConfiguration.NoinputPrompts.Count;
                if (questionNode.CollectionConfiguration.PromptConfiguration.NoinputPrompts.Count > 0)
                {
                    foreach (var noinputPrompt in questionNode.CollectionConfiguration.PromptConfiguration.NoinputPrompts)
                    {
                        /* ConvertPlayStateModelForQuestionNodeEntry(
                            questionNode.Id,
                            NOINPUT_PROMPT_TEXT_VARIABLE_NAME + retryPromptCount,
                            NOINPUT_PROMPT_SPEECH_VARIABLE_NAME + retryPromptCount,
                            beginDialog,
                            noinputPrompt, "_NoInput", retryPromptCount, noInputListSize
                        );
                         */

                        if (noinputPrompt.ConditionPromptList.Count > 0)
                        {
                            string id = questionNode.Id + "_NoInputPrompt" + retryPromptCount;
                            var conditionGroupBuilder = new ConditionGroup.Builder
                            {
                                Id = id + "_" + ObjectModelHelper.GenerateRandomID(),
                                DisplayName = id

                            };
                            // Process each list of condition transitions (separate if-blocks)
                            foreach (var conditionTransitions in noinputPrompt.ConditionPromptList)
                            {
                                foreach (var conditionModel in conditionTransitions)
                                {
                                    ProcessConditionTransitionsRetryPrompts(conditionGroupBuilder, conditionModel, beginDialog, "_CD_03", NOINPUT_PROMPT_TEXT_VARIABLE_NAME, NOINPUT_PROMPT_SPEECH_VARIABLE_NAME, questionNode.Id, retryPromptCount);
                                }
                            }
                            // Add the condition group to the trigger base actions
                            if (conditionGroupBuilder.Conditions.Count > 0)
                            {

                                beginDialog.Actions.Add(conditionGroupBuilder.Build());
                            }

                        }
                        retryPromptCount++;
                    }
                }
                //question.VoiceInputSettings.InputTimeoutResponse = ObjectModelHelper.GetSingleLineActivityWithTextAndSpeech(NOINPUT_PROMPT_TEXT_VARIABLE_NAME, NOINPUT_PROMPT_SPEECH_VARIABLE_NAME);
                retryPromptCount = 1;
                int noMatchListSize = questionNode.CollectionConfiguration.PromptConfiguration.NomatchPrompts.Count;
                if (questionNode.CollectionConfiguration.PromptConfiguration.NomatchPrompts != null)
                {
                    foreach (var noMatchPrompt in questionNode.CollectionConfiguration.PromptConfiguration.NomatchPrompts)
                    {
                        /* ConvertPlayStateModelForQuestionNodeEntry(
                            questionNode.Id,
                            NOMATCH_PROMPT_TEXT_VARIABLE_NAME + retryPromptCount,
                            NOMATCH_PROMPT_SPEECH_VARIABLE_NAME + retryPromptCount,
                            beginDialog,
                            noMatchPrompt, "_NoMatch", retryPromptCount, noMatchListSize
                        );
                         retryPromptCount++;*/
                        if (noMatchPrompt.ConditionPromptList.Count > 0)
                        {
                            string id = questionNode.Id + "_NoMatchPrompt" + retryPromptCount;
                            var conditionGroupBuilder = new ConditionGroup.Builder
                            {
                                Id = id + "_" + ObjectModelHelper.GenerateRandomID(),
                                DisplayName = id
                            };
                            // Process each list of condition transitions (separate if-blocks)
                            foreach (var conditionTransitions in noMatchPrompt.ConditionPromptList)
                            {
                                foreach (var conditionModel in conditionTransitions)
                                {
                                    ProcessConditionTransitionsRetryPrompts(conditionGroupBuilder, conditionModel, beginDialog, "_CD_04", NOMATCH_PROMPT_TEXT_VARIABLE_NAME, NOMATCH_PROMPT_SPEECH_VARIABLE_NAME, questionNode.Id, retryPromptCount);
                                }
                            }
                            // Add the condition group to the trigger base actions
                            if (conditionGroupBuilder.Conditions.Count > 0)
                            {

                                beginDialog.Actions.Add(conditionGroupBuilder.Build());
                            }

                        }
                        retryPromptCount++;
                    }
                }
            }


            //question.UnrecognizedPrompt = ObjectModelHelper.GetSingleLineActivityWithTextAndSpeech("Global.nomatch1_prompt_text", "Global.nomatch1_prompt_speech");
            String dmType = questionNode.dmType;
            String entityName = questionNode.Id;

            var closedListEntityBuilder = new ClosedListEntity.Builder();
            bool isDefaultAction = false;
            bool isSpecialCustomType = false;
            bool isDefaultActionWithCommand = false;
            question.Variable = InitializablePropertyPath.Create("Global." + questionNode.Id + "_reco");

            if (questionNode.success != null)
            {
                // Access grammar information from the question node
                string speechGrammar = null;
                string dtmfGrammar = null;

                if (questionNode.CollectionConfiguration?.VxmlProperties != null)
                {
                    speechGrammar = questionNode.CollectionConfiguration.VxmlProperties.speechgrammarname;
                    dtmfGrammar = questionNode.CollectionConfiguration.VxmlProperties.dtmfgrammarname;

                    Console.WriteLine($"Speech Grammar: {speechGrammar}");
                    Console.WriteLine($"DTMF Grammar: {dtmfGrammar}");

                    if (speechGrammar != "" && speechGrammar != null)
                    {
                        if (!grammarNameToEntityMap.ContainsKey(speechGrammar))
                        {
                            grammarNameToEntityMap[speechGrammar] = entityName;
                        }
                    }
                    if (dtmfGrammar != "" && dtmfGrammar != null)
                    {
                        if (!grammarNameToEntityMap.ContainsKey(dtmfGrammar))
                        {
                            grammarNameToEntityMap[dtmfGrammar] = entityName;
                        }
                    }
                }


                if (questionNode.success.ActionList.Count > 1 && questionNode.success.ActionList[1].isCommand == true && questionNode.success.ActionList[0].Name == "default")
                {
                    isDefaultActionWithCommand = true;
                }

                if (questionNode.success.ActionList.Count == 1 ||
                    (String.Compare("ZPCD", dmType, StringComparison.OrdinalIgnoreCase) == 0) || (String.Compare("PHON", dmType, StringComparison.OrdinalIgnoreCase) == 0) ||
                    (String.Compare("DIGT", dmType, StringComparison.OrdinalIgnoreCase) == 0) || (String.Compare("CRED", dmType, StringComparison.OrdinalIgnoreCase) == 0) ||
                    (String.Compare("CURR", dmType, StringComparison.OrdinalIgnoreCase) == 0) || isDefaultActionWithCommand)
                {
                    isDefaultAction = true;

                    if (String.Compare("ZPCD", dmType, StringComparison.OrdinalIgnoreCase) == 0)
                    {
                        question.Entity = new ZipCodePrebuiltEntity();

                    }
                    else if (String.Compare("PHON", dmType, StringComparison.OrdinalIgnoreCase) == 0)
                    {
                        question.Entity = new PhoneNumberPrebuiltEntity();

                    }
                    else if (String.Compare("DIGT", dmType, StringComparison.OrdinalIgnoreCase) == 0)
                    {
                        question.Entity = new NumberPrebuiltEntity();

                    }
                    else if (String.Compare("CRED", dmType, StringComparison.OrdinalIgnoreCase) == 0)
                    {
                        question.Entity = new NumberPrebuiltEntity();

                    }
                    else if (String.Compare("CURR", dmType, StringComparison.OrdinalIgnoreCase) == 0)
                    {
                        question.Entity = new MoneyPrebuiltEntity();

                    }
                    else
                    {
                        question.Entity = new StringPrebuiltEntity();
                    }

                }
                /* else if (String.Compare("YSNO", dmType, StringComparison.OrdinalIgnoreCase) == 0)
                 {
                     question.Entity = new BooleanPrebuiltEntity();
                 }*/
                else
                {
                    /*if (String.Compare("true", questionNode.success.ActionList[0].Name, StringComparison.OrdinalIgnoreCase) == 0 || String.Compare("false", questionNode.success.ActionList[0].Name, StringComparison.OrdinalIgnoreCase) == 0)
                    {
                        question.Entity = new BooleanPrebuiltEntity();
                        isSpecialCustomType = true;
                    }
                    else if (int.TryParse(questionNode.success.ActionList[0].Name, out _))
                    {
                        question.Entity = new NumberPrebuiltEntity();
                        isSpecialCustomType = true;
                    }*/


                    questionNode.success.ActionList = questionNode.success.ActionList.GroupBy(action => action.Name).
                                                        Select(group => group.First()).ToList();
                    foreach (var action in questionNode.success.ActionList)
                    {
                        // Determine the display name based on the action name
                        string displayName = NDFHelper.GetCustomDisplayName(action.Name);

                        // Add each ActionModel's Name as Id with custom DisplayName in ClosedListItem
                        closedListEntityBuilder.Items.Add(new ClosedListItem.Builder()
                        {
                            Id = action.Name,
                            DisplayName = displayName
                        });
                    }
                    question.Entity = new EmbeddedEntity.Builder()
                    {
                        Definition = closedListEntityBuilder

                    };

                    closedListEntityBuilder.DtmfMultipleChoiceOptions = new DtmfMultipleChoiceOptions.Builder()
                    {
                        GenerateMapping = false,
                        ReadOutOptions = false
                    };

                }
            }
            question.VoiceInputSettings = new VoiceInputSettings.Builder()
            {

            };
            question.VoiceInputSettings.DefaultValueMissingAction = EnumExpression<DefaultValueMissingActionWrapper>.Literal("Escalate");
            // question.VoiceInputSettings.DefaultValueOnSilence = new StringDataValue("MaxNoinput");
            String noMatchTextResult = "";
            String noMatchSpeechResult = "";
            MessageActivityTemplate.Builder messageActivityNoMatch = new MessageActivityTemplate.Builder();

            int n = 1;
            if (questionNode.CollectionConfiguration != null)
            {
                if (questionNode.CollectionConfiguration.PromptConfiguration.NomatchPrompts.Count > 0)
                {
                    foreach (var noMatchPrompt in questionNode.CollectionConfiguration.PromptConfiguration.NomatchPrompts)
                    {
                        if (noMatchPrompt.ConditionPromptList.Count > 0)
                        {
                            noMatchTextResult = "Global.NoMatchPromptText" + n;
                            noMatchSpeechResult = "Global.NoMatchPromptSpeech" + n;

                            ObjectModelHelper.AddSegmentsFromPromptToTemplateLine(noMatchTextResult, messageActivityNoMatch.Text);
                            //Added for metro
                            ObjectModelHelper.AddSegmentsFromPromptToTemplateLine(noMatchTextResult, messageActivityNoMatch.Speak);
                            // ObjectModelHelper.AddSegmentsFromPromptToTemplateLine(noMatchSpeechResult, messageActivityNoMatch.Speak);
                            n++;
                        }
                    }

                    question.UnrecognizedPrompt = messageActivityNoMatch;
                }

                string noInputTextResult = "";
                string noInputSpeechResult = "";
                MessageActivityTemplate.Builder messageActivityNoInput = new MessageActivityTemplate.Builder();
                n = 1;
                if (questionNode.CollectionConfiguration.PromptConfiguration.NoinputPrompts.Count > 0)
                {
                    foreach (var noInputPrompt in questionNode.CollectionConfiguration.PromptConfiguration.NoinputPrompts)
                    {
                        if (noInputPrompt.ConditionPromptList.Count > 0)
                        {
                            noInputTextResult = "Global.NoInputPromptText" + n;
                            noInputSpeechResult = "Global.NoInputPromptText" + n;
                            ObjectModelHelper.AddSegmentsFromPromptToTemplateLine(noInputTextResult, messageActivityNoInput.Text);
                            // ObjectModelHelper.AddSegmentsFromPromptToTemplateLine(noInputSpeechResult, messageActivityNoInput.Speak);
                            //added for metro
                            ObjectModelHelper.AddSegmentsFromPromptToTemplateLine(noInputTextResult, messageActivityNoInput.Speak);
                            n++;
                        }
                    }
                    //question.VoiceInputSettings.InputTimeoutResponse = StateUtility.ToReadRetryPrompts(beginDialog, questionNode.CollectionConfiguration.PromptConfiguration.NoinputPrompts);
                    question.VoiceInputSettings.InputTimeoutResponse = messageActivityNoInput;

                }

                if (questionNode.CollectionConfiguration.ThresholdConfiguration != null)
                {
                    String maxNoMatch = questionNode.CollectionConfiguration.ThresholdConfiguration.Maxnomatches;
                    if (maxNoMatch != "")
                    {
                        question.RepeatCount = int.Parse(maxNoMatch);//TODO: should this be max-turns instead?
                    }
                    String maxNoInput = questionNode.CollectionConfiguration.ThresholdConfiguration.Maxnoinputs;
                    if (maxNoInput != "")
                    {
                        question.VoiceInputSettings.RepeatCountOnSilence = int.Parse(maxNoInput);
                    }
                    else
                    {
                        question.VoiceInputSettings.RepeatCountOnSilence = 2;
                    }
                }
                question.VoiceInputSettings.SilenceDetectionTimeoutInMilliseconds = 7000;
                question.VoiceInputSettings.UtteranceEndTimeoutInMilliseconds = 7000;
                question.VoiceInputSettings.SpeechRecognitionTimeoutInMilliseconds = 7000;
                if (questionNode.CollectionConfiguration.VxmlProperties != null)
                {
                    String timeout = ObjectModelHelper.RemoveCharacters(questionNode.CollectionConfiguration.VxmlProperties.timeout);

                    if (timeout != "" && int.Parse(timeout) > 0)
                    {
                        timeout = timeout.Replace("ms", "");
                        question.VoiceInputSettings.SilenceDetectionTimeoutInMilliseconds = int.Parse(timeout);
                    }
                    String incompleteTimeout = ObjectModelHelper.RemoveCharacters(questionNode.CollectionConfiguration.VxmlProperties.incompletetimeout);
                    if (incompleteTimeout != "" && int.Parse(incompleteTimeout) > 0)
                    {
                        incompleteTimeout = incompleteTimeout.Replace("ms", "");
                        question.VoiceInputSettings.UtteranceEndTimeoutInMilliseconds = int.Parse(incompleteTimeout);
                    }

                    String maxSpeechTimeout = ObjectModelHelper.RemoveCharacters(questionNode.CollectionConfiguration.VxmlProperties.maxspeechtimeout);
                    if (maxSpeechTimeout != "" && int.Parse(maxSpeechTimeout) > 0)
                    {
                        maxSpeechTimeout = maxSpeechTimeout.Replace("ms", "");
                        question.VoiceInputSettings.SpeechRecognitionTimeoutInMilliseconds = int.Parse(maxSpeechTimeout);
                    }
                }
            }


            beginDialog.Actions.Add(question);

            string inputVariable = "Global." + questionNode.Id + "_Input";
            string variableExpression = $"Text({question.Variable})";

            beginDialog.Actions.Add(ObjectModelHelper.SetVariableToExpression(inputVariable, variableExpression, questionNode.Id));

            if (questionNode.success != null)
            {
                if (isDefaultAction && questionNode.success.ActionList[0].Name == "default")
                {

                    if (questionNode.success.ActionList.Count > 1)
                    {
                        ConditionGroup.Builder conditionGroupBuilder = new ConditionGroup.Builder()
                        {
                            Id = "conditionGroup_" + ObjectModelHelper.GenerateRandomID() + "_tv",
                            DisplayName = ObjectModelHelper.getLoggingStateName(questionNode.Id, "CG"), //+ ObjectModelHelper.GenerateRandomID(),
                        };

                        foreach (var actionList in questionNode.success.ActionList.Skip(1))
                        {
                            // Create a condition group builder once, outside the loop


                            string condition = $"{inputVariable} = \"{actionList.Name}\"";

                            // Create a condition item builder for each action
                            var conditionItemBuilder = new ConditionItem.Builder
                            {
                                Id = "condItem_" + ObjectModelHelper.getLoggingStateName(questionNode.Id, "CD") + "_" + ObjectModelHelper.GenerateRandomID(),
                                Condition = condition
                            };

                            // Add session mappings to the condition item
                            foreach (var sessionMapping in actionList.SessionMappingList)
                            {
                                if (string.IsNullOrEmpty(sessionMapping.value))
                                {
                                    conditionItemBuilder.Actions.Add(ObjectModelHelper.SetVariableToBlank("Global." + sessionMapping.key));
                                }
                                else
                                {
                                    conditionItemBuilder.Actions.Add(ObjectModelHelper.SetVariable("Global." + sessionMapping.key, sessionMapping.value, sessionMapping.type, questionNode.Id));
                                }
                            }

                            bool isDefault = false;
                            String externalCustomStateString = CreateConcatenatedEventNameString(_customStates);
                            // Process condition transitions if any exist
                            if (actionList.ConditionTransitionsList.Count > 0)
                            {
                                foreach (var conditionTransitions in actionList.ConditionTransitionsList)
                                {
                                    ConditionGroup.Builder defaultconditionGroupBuilder = new ConditionGroup.Builder()
                                    {
                                        Id = "conditionGroup_" + ObjectModelHelper.GenerateRandomID() + "_tv",
                                        DisplayName = ObjectModelHelper.getLoggingStateName(questionNode.Id, "CG")
                                    };
                                    foreach (var conditionModel in conditionTransitions)
                                    {
                                        if (conditionModel.Condition == "default")
                                        {
                                            isDefault = true;
                                            processDefaultDMEntity(beginDialog, actionList, questionNode.Id);
                                        }
                                        else
                                        {

                                            // Process non-default conditions and add them to the conditionItemBuilder
                                            ProcessConditionTransitions(defaultconditionGroupBuilder, conditionModel, beginDialog, questionNode.Id);


                                            // Only add the condition group if it has conditions
                                            // conditionItemBuilder.Actions.Add(defaultconditionGroupBuilder.Build());

                                        }
                                    }
                                    if (defaultconditionGroupBuilder.Conditions.Count > 0)
                                    {
                                        // Only add the condition group if it has conditions
                                        conditionItemBuilder.Actions.Add(defaultconditionGroupBuilder.Build());
                                    }
                                }
                            }
                            if (!string.IsNullOrEmpty(actionList.Next))
                            {
                                String gotoNextNode = actionList.Next;
                                if (externalCustomStateString.Contains(gotoNextNode))
                                {
                                    BeginDialog.Builder redirectConversation = new BeginDialog.Builder()
                                    {
                                        Id = ObjectModelHelper.GenerateRandomID(),
                                        Dialog = DialogExpression.Literal("topic." + gotoNextNode)

                                    };
                                    conditionItemBuilder.Actions.Add(redirectConversation);
                                }
                                else
                                {
                                    gotoNextNode = getNextNodeForCondition(conditionItemBuilder, gotoNextNode);

                                }
                            }


                            // Only add the condition item to the group if it's not the default case


                            conditionGroupBuilder.Conditions.Add(conditionItemBuilder.Build());


                            // Add the condition group to the dialog regardless

                        }
                        var elseActions = new List<DialogAction.Builder>();

                        processDefaultDMEntityElse(elseActions, questionNode.success.ActionList[0], beginDialog, questionNode.Id);
                        if (elseActions.Count > 0)
                        {
                            foreach (var actionBuilder in elseActions)
                            {
                                conditionGroupBuilder.ElseActions.Add(actionBuilder.Build());
                            }
                        }
                        beginDialog.Actions.Add(conditionGroupBuilder.Build());
                    }
                    else
                    {
                        //beginDialog = StateUtility.ToReadConditionTransitionDefault(beginDialog, questionNode.success);
                        foreach (var actionList in questionNode.success.ActionList)
                        {
                            processDefaultDMEntity(beginDialog, actionList, questionNode.Id);
                        }

                    }

                }
                else
                {
                    int actionCount = 0;
                    // Create a condition group builder once, outside the loop
                    ConditionGroup.Builder conditionGroupBuilder = new ConditionGroup.Builder()
                    {
                        Id = "conditionGroup_" + ObjectModelHelper.GenerateRandomID() + "_tv",
                        DisplayName = ObjectModelHelper.getLoggingStateName(questionNode.Id, "CG"),
                    };

                    foreach (var actionList in questionNode.success.ActionList)
                    {
                        actionCount = actionCount + 1;

                        // Determine the condition based on the dmType value
                        //string condition = string.Equals("YSNO", dmType, StringComparison.OrdinalIgnoreCase)
                        //    ? $"{question.Variable} = {actionList.Name}"
                        //    : $"{question.Variable} = {closedListEntityBuilder.OptionSetName}.{actionList.Name}";
                        string condition = $"{inputVariable} = \"{actionList.Name}\"";
                        /* if (isSpecialCustomType || string.Equals("YSNO", dmType, StringComparison.OrdinalIgnoreCase))
                         {
                             condition = $"{question.Variable} = {actionList.Name}";
                         }
                         else {

                             condition = $"{question.Variable} = {closedListEntityBuilder.OptionSetName}.{actionList.Name}";
                         }
 */
                        // Create a condition item builder for each action
                        var conditionItemBuilder = new ConditionItem.Builder
                        {
                            Id = "condItem_" + ObjectModelHelper.getLoggingStateName(questionNode.Id, "CD") + "_" + ObjectModelHelper.GenerateRandomID(),
                            Condition = condition
                        };

                        // Add session mappings to the condition item
                        foreach (var sessionMapping in actionList.SessionMappingList)
                        {
                            if (string.IsNullOrEmpty(sessionMapping.value))
                            {
                                conditionItemBuilder.Actions.Add(ObjectModelHelper.SetVariableToBlank("Global." + sessionMapping.key));
                            }
                            else
                            {
                                conditionItemBuilder.Actions.Add(ObjectModelHelper.SetVariable("Global." + sessionMapping.key, sessionMapping.value, sessionMapping.type, questionNode.Id));
                            }
                        }

                        if (actionList.PromptList.Count > 0)
                        {

                            StringBuilder result = new StringBuilder();
                            StringBuilder speakText = new StringBuilder();


                            foreach (var prompt in actionList.PromptList)
                            {
                                speakHelper(speakText, prompt);
                                result.Append(prompt.Text + " <br> ");
                            }
                            // Console.WriteLine("----------------------------" + speakText.ToString() + "-------------------------------------");
                            string finalString = result.ToString();
                            string finalSpeakString = speakText.ToString();
                            Console.WriteLine(finalSpeakString);
                            MessageActivityTemplate.Builder messageActivity = new MessageActivityTemplate.Builder();
                            ObjectModelHelper.AddSegmentsFromPromptToTemplateLine(finalString, messageActivity.Text);
                            // removed for metro ...we dont need audiosrc so adding text only to the template line
                            ObjectModelHelper.AddSegmentsFromPromptToTemplateLine(finalString, messageActivity.Speak);

                            string messageId = "Prompt_" + ObjectModelHelper.GenerateRandomID();

                            SendActivity.Builder sendActivity = new SendActivity.Builder()
                            {
                                Id = messageId,
                                DisplayName = messageId,
                                Activity = messageActivity,
                            };
                            conditionItemBuilder.Actions.Add(sendActivity);
                        }

                        bool isDefault = false;
                        String externalCustomStateString = CreateConcatenatedEventNameString(_customStates);
                        // Process condition transitions if any exist
                        if (actionList.ConditionTransitionsList.Count > 0)
                        {
                            foreach (var conditionTransitions in actionList.ConditionTransitionsList)
                            {
                                ConditionGroup.Builder defaultconditionGroupBuilder = new ConditionGroup.Builder()
                                {
                                    Id = "conditionGroup_" + ObjectModelHelper.GenerateRandomID() + "_tv",
                                    DisplayName = ObjectModelHelper.getLoggingStateName(questionNode.Id, "CG")
                                };
                                foreach (var conditionModel in conditionTransitions)
                                {
                                    if (conditionModel.Condition == "default")
                                    {
                                        isDefault = true;
                                        processDefaultDMEntity(beginDialog, actionList, questionNode.Id);
                                    }
                                    else
                                    {

                                        // Process non-default conditions and add them to the conditionItemBuilder
                                        ProcessConditionTransitions(defaultconditionGroupBuilder, conditionModel, beginDialog, questionNode.Id);


                                        // Only add the condition group if it has conditions
                                        // conditionItemBuilder.Actions.Add(defaultconditionGroupBuilder.Build());

                                    }
                                }
                                if (defaultconditionGroupBuilder.Conditions.Count > 0)
                                {
                                    // Only add the condition group if it has conditions
                                    conditionItemBuilder.Actions.Add(defaultconditionGroupBuilder.Build());
                                }
                            }
                        }

                        if (!string.IsNullOrEmpty(actionList.Next))
                        {
                            String gotoNextNode = actionList.Next;
                            if (externalCustomStateString.Contains(gotoNextNode))
                            {
                                BeginDialog.Builder redirectConversation = new BeginDialog.Builder()
                                {
                                    Id = ObjectModelHelper.GenerateRandomID(),
                                    Dialog = DialogExpression.Literal("topic." + gotoNextNode)

                                };
                                conditionItemBuilder.Actions.Add(redirectConversation);
                            }
                            else
                            {
                                gotoNextNode = getNextNodeForCondition(conditionItemBuilder, gotoNextNode);

                            }
                        }
                        // Only add the condition item to the group if it's not the default case
                        if (!isDefault)
                        {
                            conditionGroupBuilder.Conditions.Add(conditionItemBuilder.Build());
                        }

                    }

                    // Add the condition group to the dialog regardless
                    beginDialog.Actions.Add(conditionGroupBuilder.Build());

                }

            }
        }

        private void convertQuestionNodeWithPowerFx(TriggerBase.Builder beginDialog, DmStateModel questionNode)
        {
            string logStateName = ObjectModelHelper.getLoggingStateName(questionNode.Id, "LG");
            LogCustomTelemetryEvent.Builder startTelemetry = BuildLogTelemetryEvent(logStateName);
            beginDialog.Actions.Add(startTelemetry);
            processSessionMapping(beginDialog, questionNode.SessionMappings, questionNode.Id);

            Question.Builder question = new Question.Builder();
            question.Id = questionNode.Id;
            question.DisplayName = questionNode.Id;
            if (questionNode.CollectionConfiguration != null)
            {
                // String bargeInOverride = getNodeSettingOverride("bargein", questionNode.NodeSettingOverride);
                InterruptionPolicy.Builder interruptionPolicy = new InterruptionPolicy.Builder();
                interruptionPolicy.AllowInterruption = true;
                /*if (bargeInOverride != "")
                {
                    interruptionPolicy.AllowInterruption = false;
                }
                else
                {
                    interruptionPolicy.AllowInterruption = true;
                }*/
                question.InterruptionPolicy = interruptionPolicy;
                // question.Prompt = ObjectModelHelper.GetSingleLineActivityWithTextAndSpeech(INITIAL_PROMPT_TEXT_VARIABLE_NAME, INITIAL_PROMPT_SPEECH_VARIABLE_NAME);
                question.AlwaysPrompt = true;
                int retryPromptCount = 1;
                int noInputListSize = questionNode.CollectionConfiguration.PromptConfiguration.NoinputPrompts.Count;
                if (questionNode.CollectionConfiguration.PromptConfiguration.NoinputPrompts.Count > 0)
                {
                    foreach (var noinputPrompt in questionNode.CollectionConfiguration.PromptConfiguration.NoinputPrompts)
                    {

                        retryPromptCount++;
                    }
                }
                //question.VoiceInputSettings.InputTimeoutResponse = ObjectModelHelper.GetSingleLineActivityWithTextAndSpeech(NOINPUT_PROMPT_TEXT_VARIABLE_NAME, NOINPUT_PROMPT_SPEECH_VARIABLE_NAME);
                retryPromptCount = 1;
                int noMatchListSize = questionNode.CollectionConfiguration.PromptConfiguration.NomatchPrompts.Count;
                if (questionNode.CollectionConfiguration.PromptConfiguration.NomatchPrompts != null)
                {
                    foreach (var noMatchPrompt in questionNode.CollectionConfiguration.PromptConfiguration.NomatchPrompts)
                    {

                        retryPromptCount++;
                    }
                }
            }
        }
        private static LogCustomTelemetryEvent.Builder BuildLogTelemetryEvent(string logStateName)
        {
            LogCustomTelemetryEvent.Builder startTelemetry = new LogCustomTelemetryEvent.Builder()
            {
                Id = logStateName,
                DisplayName = logStateName,
                EventName = logStateName,
                // Properties = ValueExpression.Expression(inputLog.ToString())
            };
            return startTelemetry;
        }

        private void convertDataAccessNode(TriggerBase.Builder beginDialog, DataAccessModel daNode)
        {

            string logStateName = ObjectModelHelper.getLoggingStateName(daNode.Id, "LG");
            LogCustomTelemetryEvent.Builder startTelemetry = BuildLogTelemetryEvent(logStateName);
            beginDialog.Actions.Add(startTelemetry);
            processSessionMapping(beginDialog, daNode.sessionMappings, daNode.Id);
            string mcsNodeId = daNode.Id;
            var methodType = HttpMethodTypeWrapper.Get(HttpMethodType.Get); // changes for metro HttpMethodTypeWrapper.Get(HttpMethodType.Post);

            StringBuilder inputJSON = new StringBuilder(); // for client side
            ActionInputBinding.Builder clientSideInputs = new ActionInputBinding.Builder();
            StringBuilder body = new StringBuilder();
            StringBuilder inputLog = new StringBuilder();// for server side
            /*
             * The body will be a PowerFX record that looks like this:
             * {
             *     customer: Global.customer,
             *     profile: Global.profile
             * }
             */
            body.Append("={\n");
            inputLog.Append("={\n");
            inputJSON.Append("{\n");

            if (daNode.InputVariableList != null && daNode.InputVariableList.Count > 0)
            {
                for (int i = 0; i < daNode.InputVariableList.Count; i++)
                {
                    var input = daNode.InputVariableList[i];

                    // body.Append("\t" + input + ": " + "Global." + input);
                    inputJSON.Append(input);
                    clientSideInputs.Binding.Add(input, ValueExpression.Expression("Global." + input));

                    //body.Append($"\t{input}: Global.{input}");
                    body.Append("\t" + input + ": " + "Global." + input);
                    inputLog.Append("\t" + "input_" + daNode.Id + "_" + input + ": " + "Global." + input);
                    if (i < daNode.InputVariableList.Count - 1)
                    {
                        body.Append(",\n");
                        inputLog.Append(",\n");
                        inputJSON.Append(",\n");
                    }
                    else
                    {
                        body.Append("\n");
                        inputLog.Append("\n");
                        inputJSON.Append("\n");
                    }
                }
            }

            body.Append("}");
            inputLog.Append("}");
            inputJSON.Append("}");

            // For outputs, define the output type
            //ActionOutputBinding.Builder actionOutputBinding = new ActionOutputBinding.Builder();
            StringBuilder outputJSON = new StringBuilder();
            StringBuilder outputLog = new StringBuilder();
            outputLog.Append("={\n");
            outputJSON.Append("{\n");
            outputJSON.Append("\"returnCode\": \"0\",\n");

            //commenting to remove output variables
            // actionOutputBinding.Binding.Add("returnCode", InitializablePropertyPath.Create("Global.returnCode"));
            for (int i = 0; i < daNode.OutputVariableList.Count; i++)
            {
                var output = daNode.OutputVariableList[i].Value;
                // Assuming output is the variable name and you need to create a key-value pair
                string variableJSON = $"\"{output}\": \"{output}\""; // Replace someValue with the actual value
                if (variableJSON.Contains("\"returnCode\":")) { continue; } // Skip returnCode
                outputJSON.Append(variableJSON);
                //commenting to remove output variables
                //actionOutputBinding.Binding.Add(output, InitializablePropertyPath.Create("Global." + output));
                outputLog.Append("\t" + "output_" + daNode.Id + "_" + output + ": " + "Global." + output);

                outputJSON.Append(",\n");
                if (i < daNode.OutputVariableList.Count - 1)
                {
                    outputLog.Append(",\n");
                }
                else
                {
                    outputLog.Append("\n");

                }
            }
            if (outputJSON.Length > 2)
            {
                outputJSON.Length -= 2;
            }
            outputJSON.Append("\n}");
            outputLog.Append("}");

            string finalJSON = outputJSON.ToString();
            // Console.WriteLine("Generated JSON: " + finalJSON);

            DataType outputDataType = ObjectModelHelper.GetDataTypeFromJSON(finalJSON);

            /*
            if (daNode.ExternalFetchEnabled == true)
            {
                // client side
                // inside the topic, it should be a BeginDialog (Plugin action, which is a "Go to another topic" action)
                BeginDialog.Builder clientSideBeginDialog = new BeginDialog.Builder()
                {
                    Id = mcsNodeId,
                    Dialog = DialogExpression.Literal(mcsNodeId)
                };
                // clientSideBeginDialog.Input

                // output
                clientSideBeginDialog.Input = clientSideInputs;
                clientSideBeginDialog.Output = actionOutputBinding;
                beginDialog.Actions.Add(clientSideBeginDialog);

                // outside of the topic, there needs to be a new component
                DialogComponent.Builder clientSideComponent = new DialogComponent.Builder()
                {
                    DisplayName = mcsNodeId,
                    Description = "Client side action for " + mcsNodeId,
                    ParentBotId = _botId,
                    SchemaName = FormatNodeName(daNode.Name)
                };

                if (inputJSON.ToString() == "{\n}") { inputJSON.Clear(); inputJSON.Append("{\"returnCode\": \"0\"}"); }
                clientSideComponent.Dialog = new TaskDialog.Builder()
                {
                    Action = new InvokeClientTaskAction.Builder()
                    {
                        ClientActionInputSchema = ObjectModelHelper.GetRecordDataTypeFromJSON(inputJSON.ToString()),
                        ClientActionResponseSchema = ObjectModelHelper.GetRecordDataTypeFromJSON(outputJSON.ToString())
                    },
                    ModelDisplayName = mcsNodeId + "_ClientTask",
                    ModelDescription = "Client side DA for " + mcsNodeId + ". " + daNode.Description
                }.Build();
                _clientSideDAComponents.Add(clientSideComponent.Build());
            }
            else
            {
                JsonRequestContent.Builder httpRequestContent = new JsonRequestContent.Builder();

                httpRequestContent.Content = ValueExpression.Expression(body.ToString());
                HttpRequestAction.Builder httpRequestAction = new HttpRequestAction.Builder()
                {
                    Id = mcsNodeId,
                    Method = methodType,
                    Url = StringExpression.Expression("Concatenate(Global.dataHost,\"" + daNode?.BackendConfig?.UrlExtension + "\")"),
                    Response = InitializablePropertyPath.Create("Topic.result_from_" + mcsNodeId),
                    ResponseSchema = outputDataType,
                    Body = httpRequestContent
                };

                httpRequestAction.RequestTimeoutInMilliseconds = 30000;

                beginDialog.Actions.Add(httpRequestAction);

                foreach (var output in daNode.OutputVariableList)
                {
                    beginDialog.Actions.Add(ObjectModelHelper.SetVariableToExpression("Global."+output, "Topic.result_from_" + daNode.Id + "." + output, daNode.Id));
                }
            }*/
            LogCustomTelemetryEvent.Builder inputTelemetry = new LogCustomTelemetryEvent.Builder()
            {
                Id = "DAInputs_" + mcsNodeId,
                DisplayName = "DAInputs_" + mcsNodeId,
                EventName = "DataAccessInput",
                Properties = ValueExpression.Expression(inputLog.ToString())
            };
            //removed for Mtero
            //beginDialog.Actions.Add(inputTelemetry);

            /*string outputVariableName = "Topic.result_from_" + mcsNodeId;
            JsonRequestContent.Builder httpRequestContent = new JsonRequestContent.Builder();
            httpRequestContent.Content = ValueExpression.Expression(body.ToString());*/

            // New code: build the httpRequestAction with the structure like the YAML example
            string outputVariableName = "Global." + daNode.dataaccessId;
            HttpRequestAction.Builder httpRequestAction = new HttpRequestAction.Builder()
            {
                Id = mcsNodeId,
                DisplayName = mcsNodeId,
                Method = methodType,
                //URL assignment removed for metro                 Url = StringExpression.Expression("=Concatenate(Global.APIbaseUrl,\"" + daNode.dataaccessId + "\")"),
                Url = "https://www.microsoft.com/",
                /*Url = API_BASE_URL + daNode.dataaccessId,*/
                //StringExpression.Expression("=Concatenate(" + daNode.baseUrl + "\"," + daNode.dataaccessId + "\")"),
                //   Url = StringExpression.Expression("=Concatenate(Global.dataHost,\"" + daNode.baseUrl + "\")"),
                Response = InitializablePropertyPath.Create(outputVariableName),
                ResponseSchema = DataType.String,
                //Body = new JsonRequestContent.Builder()
                //{
                //    Content = ValueExpression.Expression(body.ToString())
                //}
            };

            //headers removed for metro
            //httpRequestAction.Headers.Add("Content-Type", "application/json");
            //httpRequestAction.Headers.Add("Authorization", "Basic YWRtaW46YWRtaW4xMjM=");
            //httpRequestAction.Headers.Add("Cookie", "=Concatenate(\"SESSION=\",Global.backendSessionId)");
            //httpRequestAction.Headers.Add("password", "=Global.password");
            //httpRequestAction.Headers.Add("username", "=Global.username");

            // Adding latency message settings
            /*httpRequestAction.LatencyMessageSettings = new LatencyMessageSettings.Builder()
            {
                AllowLatencyMessage = true,
                LatencyMessageDelayInMilliseconds = 501,
                MinimumPlaybackInMilliseconds = 501
            };*/


            beginDialog.Actions.Add(httpRequestAction);
            foreach (var output in daNode.OutputVariableList)
            {
                var type = "";
                if (output.Type.Equals("boolean"))
                {
                    type = "Boolean";
                }
                else if (output.Type.Equals("integer"))
                {
                    type = "Value";
                }
                else
                {
                    type = "Text";
                }
                //commenting to remove output variables
                //beginDialog.Actions.Add(ObjectModelHelper.SetVariableToExpression("Global." + output.Value, type+"("+"Global." + daNode.dataaccessId + "." + output.Value+")", daNode.Id));
            }

            LogCustomTelemetryEvent.Builder outputTelemetry = new LogCustomTelemetryEvent.Builder()
            {
                Id = "DAOutputs_" + mcsNodeId,
                DisplayName = "LogOutputs_" + mcsNodeId,
                EventName = "DataAccessOutput",
                Properties = ValueExpression.Expression(outputLog.ToString())
            };
            //   beginDialog.Actions.Add(outputTelemetry);

            // Handle any additional session mappings or actions
            if (daNode.ConditionTransitionsList.Count > 0)
            {
                // Initialize the top-level condition group builder
                var conditionGroupBuilder = new ConditionGroup.Builder
                {
                    Id = daNode.Id + "_tv", //+ ObjectModelHelper.GenerateRandomID(),
                    DisplayName = ObjectModelHelper.getLoggingStateName(daNode.Id, "CG") //+ ObjectModelHelper.GenerateRandomID()
                };

                // Process each list of condition transitions (separate if-blocks)
                foreach (var conditionTransitions in daNode.ConditionTransitionsList)
                {
                    foreach (var conditionModel in conditionTransitions)
                    {
                        ProcessConditionTransitions(conditionGroupBuilder, conditionModel, beginDialog, daNode.Id);
                    }
                }

                // Add the condition group to the trigger base actions
                if (conditionGroupBuilder.Conditions.Count > 0)
                {
                    beginDialog.Actions.Add(conditionGroupBuilder.Build());
                }
            }

        }

        private void convertToPlaceHolderDataAccessNode(TriggerBase.Builder beginDialog, DataAccessModel daNode)
        {

            string logStateName = ObjectModelHelper.getLoggingStateName(daNode.Id, "LG");
            LogCustomTelemetryEvent.Builder startTelemetry = BuildLogTelemetryEvent(logStateName);
            beginDialog.Actions.Add(startTelemetry);
            processSessionMapping(beginDialog, daNode.sessionMappings, daNode.Id);
            string mcsNodeId = daNode.Id;
            var methodType = HttpMethodTypeWrapper.Get(HttpMethodType.Get); // changes for metro HttpMethodTypeWrapper.Get(HttpMethodType.Post);

            StringBuilder inputJSON = new StringBuilder(); // for client side
            ActionInputBinding.Builder clientSideInputs = new ActionInputBinding.Builder();
            StringBuilder body = new StringBuilder();
            StringBuilder inputLog = new StringBuilder();// for server side
            /*
             * The body will be a PowerFX record that looks like this:
             * {
             *     customer: Global.customer,
             *     profile: Global.profile
             * }
             */
            body.Append("={\n");
            inputLog.Append("={\n");
            inputJSON.Append("{\n");

            if (daNode.InputVariableList != null && daNode.InputVariableList.Count > 0)
            {
                for (int i = 0; i < daNode.InputVariableList.Count; i++)
                {
                    var input = daNode.InputVariableList[i];

                    // body.Append("\t" + input + ": " + "Global." + input);
                    inputJSON.Append(input);
                    clientSideInputs.Binding.Add(input, ValueExpression.Expression("Global." + input));

                    //body.Append($"\t{input}: Global.{input}");
                    body.Append("\t" + input + ": " + "Global." + input);
                    inputLog.Append("\t" + "input_" + daNode.Id + "_" + input + ": " + "Global." + input);
                    if (i < daNode.InputVariableList.Count - 1)
                    {
                        body.Append(",\n");
                        inputLog.Append(",\n");
                        inputJSON.Append(",\n");
                    }
                    else
                    {
                        body.Append("\n");
                        inputLog.Append("\n");
                        inputJSON.Append("\n");
                    }
                }
            }

            body.Append("}");
            inputLog.Append("}");
            inputJSON.Append("}");

            // For outputs, define the output type
            //ActionOutputBinding.Builder actionOutputBinding = new ActionOutputBinding.Builder();
            StringBuilder outputJSON = new StringBuilder();
            StringBuilder outputLog = new StringBuilder();
            outputLog.Append("={\n");
            outputJSON.Append("{\n");
            outputJSON.Append("\"returnCode\": \"0\",\n");

            //commenting to remove output variables
            // actionOutputBinding.Binding.Add("returnCode", InitializablePropertyPath.Create("Global.returnCode"));
            for (int i = 0; i < daNode.OutputVariableList.Count; i++)
            {
                var output = daNode.OutputVariableList[i].Value;
                // Assuming output is the variable name and you need to create a key-value pair
                string variableJSON = $"\"{output}\": \"{output}\""; // Replace someValue with the actual value
                if (variableJSON.Contains("\"returnCode\":")) { continue; } // Skip returnCode
                outputJSON.Append(variableJSON);
                //commenting to remove output variables
                //actionOutputBinding.Binding.Add(output, InitializablePropertyPath.Create("Global." + output));
                outputLog.Append("\t" + "output_" + daNode.Id + "_" + output + ": " + "Global." + output);

                outputJSON.Append(",\n");
                if (i < daNode.OutputVariableList.Count - 1)
                {
                    outputLog.Append(",\n");
                }
                else
                {
                    outputLog.Append("\n");

                }
            }
            if (outputJSON.Length > 2)
            {
                outputJSON.Length -= 2;
            }
            outputJSON.Append("\n}");
            outputLog.Append("}");

            string finalJSON = outputJSON.ToString();
            // Console.WriteLine("Generated JSON: " + finalJSON);

            DataType outputDataType = ObjectModelHelper.GetDataTypeFromJSON(finalJSON);

            LogCustomTelemetryEvent.Builder inputTelemetry = new LogCustomTelemetryEvent.Builder()
            {
                Id = "DAInputs_" + mcsNodeId,
                DisplayName = "DAInputs_" + mcsNodeId,
                EventName = "DataAccessInput",
                Properties = ValueExpression.Expression(inputLog.ToString())
            };
            //removed for Mtero
            //beginDialog.Actions.Add(inputTelemetry);

            /*string outputVariableName = "Topic.result_from_" + mcsNodeId;
            JsonRequestContent.Builder httpRequestContent = new JsonRequestContent.Builder();
            httpRequestContent.Content = ValueExpression.Expression(body.ToString());*/

            // New code: build the httpRequestAction with the structure like the YAML example
            string outputVariableName = "Global." + daNode.dataaccessId;
            HttpRequestAction.Builder httpRequestAction = new HttpRequestAction.Builder()
            {
                Id = mcsNodeId,
                DisplayName = mcsNodeId,
                Method = methodType,
                //URL assignment removed for metro                 Url = StringExpression.Expression("=Concatenate(Global.APIbaseUrl,\"" + daNode.dataaccessId + "\")"),
                Url = "https://www.microsoft.com/",
                /*Url = API_BASE_URL + daNode.dataaccessId,*/
                //StringExpression.Expression("=Concatenate(" + daNode.baseUrl + "\"," + daNode.dataaccessId + "\")"),
                //   Url = StringExpression.Expression("=Concatenate(Global.dataHost,\"" + daNode.baseUrl + "\")"),
                Response = InitializablePropertyPath.Create(outputVariableName),
                ResponseSchema = DataType.String,
                //Body = new JsonRequestContent.Builder()
                //{
                //    Content = ValueExpression.Expression(body.ToString())
                //}
            };

            //headers removed for metro
            //httpRequestAction.Headers.Add("Content-Type", "application/json");
            //httpRequestAction.Headers.Add("Authorization", "Basic YWRtaW46YWRtaW4xMjM=");
            //httpRequestAction.Headers.Add("Cookie", "=Concatenate(\"SESSION=\",Global.backendSessionId)");
            //httpRequestAction.Headers.Add("password", "=Global.password");
            //httpRequestAction.Headers.Add("username", "=Global.username");

            // Adding latency message settings
            /*httpRequestAction.LatencyMessageSettings = new LatencyMessageSettings.Builder()
            {
                AllowLatencyMessage = true,
                LatencyMessageDelayInMilliseconds = 501,
                MinimumPlaybackInMilliseconds = 501
            };*/


            beginDialog.Actions.Add(httpRequestAction);
            foreach (var output in daNode.OutputVariableList)
            {
                var type = "";
                if (output.Type.Equals("boolean"))
                {
                    type = "Boolean";
                }
                else if (output.Type.Equals("integer"))
                {
                    type = "Value";
                }
                else
                {
                    type = "Text";
                }
                //commenting to remove output variables
                //beginDialog.Actions.Add(ObjectModelHelper.SetVariableToExpression("Global." + output.Value, type+"("+"Global." + daNode.dataaccessId + "." + output.Value+")", daNode.Id));
            }

            LogCustomTelemetryEvent.Builder outputTelemetry = new LogCustomTelemetryEvent.Builder()
            {
                Id = "DAOutputs_" + mcsNodeId,
                DisplayName = "LogOutputs_" + mcsNodeId,
                EventName = "DataAccessOutput",
                Properties = ValueExpression.Expression(outputLog.ToString())
            };
            //   beginDialog.Actions.Add(outputTelemetry);

            // Handle any additional session mappings or actions
            if (daNode.ConditionTransitionsList.Count > 0)
            {
                // Initialize the top-level condition group builder
                var conditionGroupBuilder = new ConditionGroup.Builder
                {
                    Id = daNode.Id + "_tv", //+ ObjectModelHelper.GenerateRandomID(),
                    DisplayName = ObjectModelHelper.getLoggingStateName(daNode.Id, "CG") //+ ObjectModelHelper.GenerateRandomID()
                };

                // Process each list of condition transitions (separate if-blocks)
                foreach (var conditionTransitions in daNode.ConditionTransitionsList)
                {
                    foreach (var conditionModel in conditionTransitions)
                    {
                        ProcessConditionTransitions(conditionGroupBuilder, conditionModel, beginDialog, daNode.Id);
                    }
                }

                // Add the condition group to the trigger base actions
                if (conditionGroupBuilder.Conditions.Count > 0)
                {
                    beginDialog.Actions.Add(conditionGroupBuilder.Build());
                }
            }

        }
        private void processDefaultDMEntityElse(List<DialogAction.Builder> elseAction, SuccessActionModel defaultElseAction, TriggerBase.Builder beginDialog, String stateId)
        {

            if (defaultElseAction.ConditionTransitionsList.Count > 0)
            {
                ConditionGroup.Builder defaultconditionGroupBuilder = new ConditionGroup.Builder()
                {
                    Id = "conditionGroup_" + ObjectModelHelper.GenerateRandomID() + "_tv",
                    DisplayName = ObjectModelHelper.getLoggingStateName(stateId, "CG") //+ ObjectModelHelper.GenerateRandomID()
                };
                foreach (var conditionTransitions in defaultElseAction.ConditionTransitionsList)
                {
                    foreach (var conditionModel in conditionTransitions)
                    {

                        // Process non-default conditions and add them to the conditionItemBuilder
                        // ProcessConditionTransitions(defaultconditionGroupBuilder, conditionModel, beginDialog, null);
                        ProcessConditionTransitions(defaultconditionGroupBuilder, conditionModel, beginDialog, stateId);
                    }
                }
                elseAction.Add(defaultconditionGroupBuilder.Build());
            }
            if (defaultElseAction.SessionMappingList.Count > 0)
            {
                foreach (var sessionMapping in defaultElseAction.SessionMappingList)
                {
                    if (string.IsNullOrEmpty(sessionMapping.value))
                    {
                        elseAction.Add(ObjectModelHelper.SetVariableToBlank("Global." + sessionMapping.key));
                    }
                    else
                    {
                        elseAction.Add(ObjectModelHelper.SetVariable("Global." + sessionMapping.key, sessionMapping.value, sessionMapping.type, stateId));
                    }
                }
            }
            if (!string.IsNullOrEmpty(defaultElseAction.Next))
            {
                String externalCustomStateString = CreateConcatenatedEventNameString(_customStates);
                String gotoNextNode = defaultElseAction.Next;
                if (gotoNextNode != null)
                {
                    if (externalCustomStateString.Contains(gotoNextNode))
                    {
                        BeginDialog.Builder redirectConversation = new BeginDialog.Builder()
                        {
                            Id = ObjectModelHelper.GenerateRandomID(),
                            Dialog = DialogExpression.Literal("topic." + gotoNextNode)

                        };
                        elseAction.Add(redirectConversation);
                    }
                    else
                    {
                        gotoNextNode = getNextNodeForElseAction(elseAction, gotoNextNode);

                        if (defaultElseAction.PromptList.Count > 0)
                        {
                            StringBuilder result = new StringBuilder();
                            StringBuilder speakText = new StringBuilder();

                            foreach (var prompt in defaultElseAction.PromptList)
                            {
                                speakHelper(speakText, prompt);
                            }
                            // Console.WriteLine("----------------------------" + speakText.ToString() + "-------------------------------------");
                            foreach (var prompt in defaultElseAction.PromptList)
                            {
                                result.Append(prompt.Text + " <br> ");
                            }
                            string finalString = result.ToString();
                            string finalSpeakString = speakText.ToString();
                            Console.WriteLine(finalSpeakString);
                            MessageActivityTemplate.Builder messageActivity = new MessageActivityTemplate.Builder();
                            ObjectModelHelper.AddSegmentsFromPromptToTemplateLine(finalString, messageActivity.Text);
                            // ObjectModelHelper.AddSegmentsFromPromptToTemplateLine(finalSpeakString, messageActivity.Speak);
                            //aded for metro 
                            ObjectModelHelper.AddSegmentsFromPromptToTemplateLine(finalString, messageActivity.Speak);

                            string messageId = "Prompt_" + ObjectModelHelper.GenerateRandomID();

                            SendActivity.Builder sendActivity = new SendActivity.Builder()
                            {
                                Id = messageId,
                                DisplayName = messageId,
                                Activity = messageActivity,
                            };
                            elseAction.Add(sendActivity);
                        }

                    }
                }
            }

        }

        private void processDefaultDMEntity(TriggerBase.Builder beginDialog, SuccessActionModel successAction, String stateId)
        {
            if (successAction != null)
            {
                if (successAction.Name == "default")
                {
                    if (successAction.PromptList.Count > 0)
                    {

                        StringBuilder result = new StringBuilder();
                        StringBuilder speakText = new StringBuilder();


                        foreach (var prompt in successAction.PromptList)
                        {
                            speakHelper(speakText, prompt);
                        }
                        // Console.WriteLine("----------------------------" + speakText.ToString() + "-------------------------------------");
                        foreach (var prompt in successAction.PromptList)
                        {
                            result.Append(prompt.Text + " <br> ");
                        }
                        string finalString = result.ToString();
                        string finalSpeakString = speakText.ToString();
                        Console.WriteLine(finalSpeakString);
                        MessageActivityTemplate.Builder messageActivity = new MessageActivityTemplate.Builder();
                        ObjectModelHelper.AddSegmentsFromPromptToTemplateLine(finalString, messageActivity.Text);
                        //ObjectModelHelper.AddSegmentsFromPromptToTemplateLine(finalSpeakString, messageActivity.Speak);
                        //added for metro
                        ObjectModelHelper.AddSegmentsFromPromptToTemplateLine(finalString, messageActivity.Speak);

                        string messageId = "Prompt_" + ObjectModelHelper.GenerateRandomID();

                        SendActivity.Builder sendActivity = new SendActivity.Builder()
                        {
                            Id = messageId,
                            DisplayName = messageId,
                            Activity = messageActivity,
                        };
                        beginDialog.Actions.Add(sendActivity);
                    }
                    if (successAction.SessionMappingList.Count > 0)
                    {
                        processSessionMapping(beginDialog, successAction.SessionMappingList, stateId);
                    }
                    if (successAction.ConditionTransitionsList.Count > 0)
                    {
                        ConditionGroup.Builder defaultconditionGroupBuilder = new ConditionGroup.Builder()
                        {
                            Id = "conditionGroup_" + ObjectModelHelper.GenerateRandomID() + "_tv",
                            DisplayName = ObjectModelHelper.getLoggingStateName(stateId, "CG") //+ ObjectModelHelper.GenerateRandomID()
                        };
                        foreach (var conditionTransitions in successAction.ConditionTransitionsList)
                        {
                            foreach (var conditionModel in conditionTransitions)
                            {

                                // Process non-default conditions and add them to the conditionItemBuilder
                                ProcessConditionTransitions(defaultconditionGroupBuilder, conditionModel, beginDialog, stateId);

                            }
                        }
                        beginDialog.Actions.Add(defaultconditionGroupBuilder.Build());
                    }
                    if (!string.IsNullOrEmpty(successAction.Next))
                    {
                        String externalCustomStateString = CreateConcatenatedEventNameString(_customStates);
                        String gotoNextNode = successAction.Next;
                        if (gotoNextNode != null)
                        {
                            if (externalCustomStateString.Contains(gotoNextNode))
                            {
                                BeginDialog.Builder redirectConversation = new BeginDialog.Builder()
                                {
                                    Id = ObjectModelHelper.GenerateRandomID(),
                                    Dialog = DialogExpression.Literal("topic." + gotoNextNode)

                                };
                                beginDialog.Actions.Add(redirectConversation);
                            }
                            else
                            {
                                gotoNextNode = getNextNodeForBeginDialog(beginDialog, gotoNextNode);

                            }
                        }
                    }
                }

            }

        }

        private static string getNextNodeForBeginDialog(TriggerBase.Builder beginDialog, string gotoNextNode)
        {
            //if (gotoNextNode.EndsWith("_DM"))
            //{
            //   // gotoNextNode = gotoNextNode + "_CD_00";
            //    var gotoAction = new GotoAction.Builder()
            //    {
            //        Id = ObjectModelHelper.GenerateRandomID(),
            //        ActionId = gotoNextNode
            //    };
            //    beginDialog.Actions.Add(gotoAction);
            //}
            //if (gotoNextNode.EndsWith("_DS"))
            //{
            //    gotoNextNode += "_00";
            //    var gotoAction = new GotoAction.Builder()
            //    {
            //        Id = ObjectModelHelper.GenerateRandomID(),
            //        ActionId = gotoNextNode
            //    };
            //    beginDialog.Actions.Add(gotoAction);
            //}
            if (gotoNextNode.EndsWith(".dvxml"))
            {
                gotoNextNode = gotoNextNode.Replace(".dvxml", "");
                BeginDialog.Builder redirectDialog = new BeginDialog.Builder()
                {
                    Id = ObjectModelHelper.GenerateRandomID(),
                    Dialog = DialogExpression.Literal("topic." + gotoNextNode)
                };
                beginDialog.Actions.Add(redirectDialog);

            }
            //else if (gotoNextNode.EndsWith("_DB") || gotoNextNode.EndsWith("_DA"))
            //{
            //    gotoNextNode = "DAInputs_" + gotoNextNode;
            //    var gotoAction = new GotoAction.Builder()
            //    {
            //        Id = ObjectModelHelper.GenerateRandomID(),
            //        ActionId = gotoNextNode
            //    };
            //    beginDialog.Actions.Add(gotoAction);
            //}
            else
            {
                // Fix for CS0120: Convert the method call to use the current instance of the class (this).  
                var gotoAction = new GotoAction.Builder
                {
                    Id = ObjectModelHelper.GenerateRandomID(),
                    ActionId = ObjectModelHelper.getLoggingStateName(gotoNextNode, "LG")
                };
                beginDialog.Actions.Add(gotoAction.Build());
            }

            return gotoNextNode;
        }

        private void ProcessConditionTransitionsRetryPrompts(ConditionGroup.Builder conditionGroupBuilder, ConditionModel conditionModel, TriggerBase.Builder triggerBase, string promptType, string textPromptVariableName, string speechPromptVariableName, string nodeName, int retryPromptCount)
        {
            var conditionItemBuilders = new List<ConditionItem.Builder>();
            var elseActions = new List<DialogAction.Builder>();

            var transition = conditionModel.Transitions;

            if (conditionModel.Condition == "default") //&& promptType != "_CD_02") - removed to get rid of default true condition
            {
                HandleDefaultConditionRetryPrompts(transition, triggerBase, promptType, textPromptVariableName, speechPromptVariableName, nodeName, retryPromptCount);
            }
            else if (conditionModel.Condition == "else")
            {
                CollectElseActionsRetryPrompts(transition, elseActions, promptType, textPromptVariableName, speechPromptVariableName, nodeName, retryPromptCount);
            }
            else
            {
                var conditionItemBuilder = CreateConditionItemRetryPrompts(conditionModel.Condition, transition, triggerBase, promptType, textPromptVariableName, speechPromptVariableName, nodeName, retryPromptCount);

                // Process inner conditions
                if (transition.InnerConditionTransitions.Count > 0)
                {
                    // Create a nested condition group for inner conditions
                    var nestedConditionGroupBuilder = new ConditionGroup.Builder
                    {
                        Id = "conditionGroup_" + ObjectModelHelper.GenerateRandomID(),
                        DisplayName = ObjectModelHelper.getLoggingStateName(nodeName, "CG") //+ ObjectModelHelper.GenerateRandomID()
                    };

                    foreach (var innerCondition in transition.InnerConditionTransitions)
                    {
                        ProcessConditionTransitionsRetryPrompts(nestedConditionGroupBuilder, innerCondition, triggerBase, promptType, textPromptVariableName, speechPromptVariableName, nodeName, retryPromptCount);
                    }

                    // Add the nested condition group to the condition item actions
                    if (nestedConditionGroupBuilder.Conditions.Any() || nestedConditionGroupBuilder.ElseActions.Any())
                    {
                        conditionItemBuilder.Actions.Add(nestedConditionGroupBuilder.Build());
                    }
                }

                conditionItemBuilders.Add(conditionItemBuilder);
            }

            // Add condition items to the condition group builder
            foreach (var conditionItemBuilder in conditionItemBuilders)
            {
                conditionGroupBuilder.Conditions.Add(conditionItemBuilder.Build());
            }

            // Add collected `else` actions to the condition group builder if there are any
            if (elseActions.Count > 0)
            {
                foreach (var actionBuilder in elseActions)
                {
                    conditionGroupBuilder.ElseActions.Add(actionBuilder.Build());
                }
            }
        }

        private void HandleDefaultConditionRetryPrompts(TransitionModel transitionValue, TriggerBase.Builder triggerBase, string promptType, string textPromptVariableName, string speechPromptVariableName, String nodeName, int retryPromptCount)
        {
            StringBuilder promptBuilderText;
            StringBuilder promptBuilderSpeech;

            if (retryPromptCount > 0)
            {
                promptBuilderText = new StringBuilder($"Concatenate({textPromptVariableName + retryPromptCount}");
                promptBuilderSpeech = new StringBuilder($"Concatenate({speechPromptVariableName + retryPromptCount}");
            }
            else
            {
                promptBuilderText = new StringBuilder($"Concatenate({textPromptVariableName}");
                promptBuilderSpeech = new StringBuilder($"Concatenate({speechPromptVariableName}");

            }

            foreach (var promptModel in transitionValue.PromptList)
            {

                if (!string.IsNullOrEmpty(promptModel.Text))
                {
                    buildTextPrompt(promptBuilderText, promptModel);
                }

            }
            foreach (var promptModel in transitionValue.PromptList)
            {
                if (!string.IsNullOrEmpty(promptModel.Text))
                {
                    buildAudioPrompt(promptBuilderSpeech, promptModel);
                }

            }

            promptBuilderText.Append(")");
            promptBuilderSpeech.Append(")");

            if (promptType.Contains("_CD_03"))
            {
                triggerBase.Actions.Add(ObjectModelHelper.SetVariableToExpression(NOINPUT_PROMPT_TEXT_VARIABLE_NAME + retryPromptCount, @"Text("""")", nodeName + "_" + ObjectModelHelper.GenerateRandomID()));
                triggerBase.Actions.Add(ObjectModelHelper.SetVariableToExpression(NOINPUT_PROMPT_SPEECH_VARIABLE_NAME + retryPromptCount, @"Text("""")", nodeName + "_" + ObjectModelHelper.GenerateRandomID()));
            }
            else if (promptType.Contains("_CD_04"))
            {
                triggerBase.Actions.Add(ObjectModelHelper.SetVariableToExpression(NOMATCH_PROMPT_TEXT_VARIABLE_NAME + retryPromptCount, @"Text("""")", nodeName + "_" + ObjectModelHelper.GenerateRandomID()));
                triggerBase.Actions.Add(ObjectModelHelper.SetVariableToExpression(NOMATCH_PROMPT_SPEECH_VARIABLE_NAME + retryPromptCount, @"Text("""")", nodeName + "_" + ObjectModelHelper.GenerateRandomID()));
            }
            else
            {
                triggerBase.Actions.Add(ObjectModelHelper.SetVariableToExpression(INITIAL_PROMPT_TEXT_VARIABLE_NAME, @"Text("""")", nodeName + "_" + ObjectModelHelper.GenerateRandomID()));
                triggerBase.Actions.Add(ObjectModelHelper.SetVariableToExpression(INITIAL_PROMPT_SPEECH_VARIABLE_NAME, @"Text("""")", nodeName + "_" + ObjectModelHelper.GenerateRandomID()));
            }
            if (retryPromptCount > 0)
            {
                triggerBase.Actions.Add(ObjectModelHelper.SetVariableToExpression(textPromptVariableName + retryPromptCount, promptBuilderText.ToString(), nodeName + "_" + ObjectModelHelper.GenerateRandomID()));
                // triggerBase.Actions.Add(ObjectModelHelper.SetVariableToExpression(speechPromptVariableName + retryPromptCount, promptBuilderSpeech.ToString(), nodeName + "_" + ObjectModelHelper.GenerateRandomID()));
                //for metro
                triggerBase.Actions.Add(ObjectModelHelper.SetVariableToExpression(speechPromptVariableName + retryPromptCount, promptBuilderText.ToString(), nodeName + "_" + ObjectModelHelper.GenerateRandomID()));
            }
            else
            {
                triggerBase.Actions.Add(ObjectModelHelper.SetVariableToExpression(textPromptVariableName, promptBuilderText.ToString(), nodeName + "_" + ObjectModelHelper.GenerateRandomID()));
                //  triggerBase.Actions.Add(ObjectModelHelper.SetVariableToExpression(speechPromptVariableName, promptBuilderSpeech.ToString(), nodeName + "_" + ObjectModelHelper.GenerateRandomID()));
                // for metro
                triggerBase.Actions.Add(ObjectModelHelper.SetVariableToExpression(speechPromptVariableName, promptBuilderText.ToString(), nodeName + "_" + ObjectModelHelper.GenerateRandomID()));
            }
        }

        static void buildAudioPrompt(StringBuilder promptBuilderSpeech, PromptModel promptModel)
        {
            string escapedText = promptModel.Text.Replace("\"", "\\\"");
            if (IsVariable(promptModel.Text))
            {
                // Append variable name directly
                promptBuilderSpeech.Append($",{promptModel.Text}");
            }
            else
            {
                // Append literal string enclosed in quotes and include audio tag without escaping src quotes
                string audioTag = $"<audio src=\"\"\", Global.audioBaseUrl , \"/{promptModel.PromptId}.wav\"\"> {escapedText} </audio>";
                promptBuilderSpeech.Append($",\"{audioTag}\"");
            }
        }

        static void buildTextPrompt(StringBuilder promptBuilderText, PromptModel promptModel)
        {
            string escapedText = promptModel.Text.Replace("\"", "\\\"");

            // Append the value correctly based on whether it is a variable or a literal
            if (IsVariable(promptModel.Text))
            {
                // Append variable name directly
                promptBuilderText.Append($",{promptModel.Text}");
            }
            else
            {
                // Append literal string enclosed in quotes
                promptBuilderText.Append($",\"{escapedText}\"");
            }
        }
        private ConditionItem.Builder CreateConditionItemRetryPrompts(string condition, TransitionModel transitionValue, TriggerBase.Builder triggerBase, string promptType, string textPromptVariableName, string speechPromptVariableName, string nodeName, int retryPromptCount)
        {
            if (condition == "default")
            {
                condition = "=true";
            }
            var conditionItemBuilder = new ConditionItem.Builder
            {
                // Id = "conditionItem_" + nodeName + promptType + ObjectModelHelper.GenerateRandomID(),
                Id = "condItem_" + ObjectModelHelper.getLoggingStateName(nodeName, "CD") + "_" + ObjectModelHelper.GenerateRandomID(),
                Condition = RemoveDuplicateGlobals(StateUtility.TransformCondition(condition))
            };

            StringBuilder promptBuilderText;
            StringBuilder promptBuilderSpeech;

            if (retryPromptCount > 0)
            {
                promptBuilderText = new StringBuilder($"Concatenate({textPromptVariableName + retryPromptCount}");
                promptBuilderSpeech = new StringBuilder($"Concatenate({speechPromptVariableName + retryPromptCount}");
            }
            else
            {
                promptBuilderText = new StringBuilder($"Concatenate({textPromptVariableName}");
                promptBuilderSpeech = new StringBuilder($"Concatenate({speechPromptVariableName}");

            }

            if (transitionValue.PromptList.Count > 0)
            {
                foreach (var promptModel in transitionValue.PromptList)
                {
                    if (!string.IsNullOrEmpty(promptModel.Text))
                    {
                        buildTextPrompt(promptBuilderText, promptModel);
                    }
                }

                foreach (var promptModel in transitionValue.PromptList)
                {
                    if (!string.IsNullOrEmpty(promptModel.Text))
                    {
                        buildAudioPrompt(promptBuilderSpeech, promptModel);
                    }
                }

                promptBuilderText.Append(")");
                promptBuilderSpeech.Append(")");

                if (promptType.Contains("_CD_03"))
                {
                    conditionItemBuilder.Actions.Add(ObjectModelHelper.SetVariableToExpression(NOINPUT_PROMPT_TEXT_VARIABLE_NAME + retryPromptCount, @"Text("""")", nodeName + "_" + ObjectModelHelper.GenerateRandomID()));
                    conditionItemBuilder.Actions.Add(ObjectModelHelper.SetVariableToExpression(NOINPUT_PROMPT_SPEECH_VARIABLE_NAME + retryPromptCount, @"Text("""")", nodeName + "_" + ObjectModelHelper.GenerateRandomID()));
                }
                else if (promptType.Contains("_CD_04"))
                {
                    conditionItemBuilder.Actions.Add(ObjectModelHelper.SetVariableToExpression(NOMATCH_PROMPT_TEXT_VARIABLE_NAME + retryPromptCount, @"Text("""")", nodeName + "_" + ObjectModelHelper.GenerateRandomID()));
                    conditionItemBuilder.Actions.Add(ObjectModelHelper.SetVariableToExpression(NOMATCH_PROMPT_SPEECH_VARIABLE_NAME + retryPromptCount, @"Text("""")", nodeName + "_" + ObjectModelHelper.GenerateRandomID()));
                }
                else
                {
                    conditionItemBuilder.Actions.Add(ObjectModelHelper.SetVariableToExpression(INITIAL_PROMPT_TEXT_VARIABLE_NAME, @"Text("""")", nodeName + "_" + ObjectModelHelper.GenerateRandomID()));
                    conditionItemBuilder.Actions.Add(ObjectModelHelper.SetVariableToExpression(INITIAL_PROMPT_SPEECH_VARIABLE_NAME, @"Text("""")", nodeName + "_" + ObjectModelHelper.GenerateRandomID()));
                }
                if (retryPromptCount > 0)
                {

                    conditionItemBuilder.Actions.Add(ObjectModelHelper.SetVariableToExpression(textPromptVariableName + retryPromptCount, promptBuilderText.ToString(), nodeName + "_" + ObjectModelHelper.GenerateRandomID()));
                    //  conditionItemBuilder.Actions.Add(ObjectModelHelper.SetVariableToExpression(speechPromptVariableName + retryPromptCount, promptBuilderSpeech.ToString(), nodeName + "_" + ObjectModelHelper.GenerateRandomID()));
                    // for metro
                    conditionItemBuilder.Actions.Add(ObjectModelHelper.SetVariableToExpression(speechPromptVariableName + retryPromptCount, promptBuilderText.ToString(), nodeName + "_" + ObjectModelHelper.GenerateRandomID()));
                }
                else
                {
                    conditionItemBuilder.Actions.Add(ObjectModelHelper.SetVariableToExpression(textPromptVariableName, promptBuilderText.ToString(), nodeName + "_" + ObjectModelHelper.GenerateRandomID()));
                    // conditionItemBuilder.Actions.Add(ObjectModelHelper.SetVariableToExpression(speechPromptVariableName, promptBuilderSpeech.ToString(), nodeName + "_" + ObjectModelHelper.GenerateRandomID()));
                    // for metro
                    conditionItemBuilder.Actions.Add(ObjectModelHelper.SetVariableToExpression(speechPromptVariableName, promptBuilderText.ToString(), nodeName + "_" + ObjectModelHelper.GenerateRandomID()));
                }
            }

            return conditionItemBuilder;
        }

        private void CollectElseActionsRetryPrompts(TransitionModel transitionValue, List<DialogAction.Builder> elseActions, string promptType, string textPromptVariableName, string speechPromptVariableName, String nodeName, int retryPromptCount)
        {
            StringBuilder promptBuilderText;
            StringBuilder promptBuilderSpeech;

            if (retryPromptCount > 0)
            {
                promptBuilderText = new StringBuilder($"Concatenate({textPromptVariableName + retryPromptCount}");
                promptBuilderSpeech = new StringBuilder($"Concatenate({speechPromptVariableName + retryPromptCount}");
            }
            else
            {
                promptBuilderText = new StringBuilder($"Concatenate({textPromptVariableName}");
                promptBuilderSpeech = new StringBuilder($"Concatenate({speechPromptVariableName}");
            }

            if (transitionValue.PromptList.Count > 0)
            {
                foreach (var promptModel in transitionValue.PromptList)
                {
                    if (!string.IsNullOrEmpty(promptModel.Text))
                    {
                        buildTextPrompt(promptBuilderText, promptModel);
                    }

                }

                foreach (var promptModel in transitionValue.PromptList)
                {
                    if (!string.IsNullOrEmpty(promptModel.Text))
                    {
                        buildAudioPrompt(promptBuilderSpeech, promptModel);
                    }

                }

                // Close the Concatenate function
                promptBuilderText.Append(")");
                promptBuilderSpeech.Append(")");

                // Add prompt actions based on promptType
                if (promptType.Contains("_CD_03"))
                {
                    elseActions.Add(ObjectModelHelper.SetVariableToExpression(NOINPUT_PROMPT_TEXT_VARIABLE_NAME + retryPromptCount, @"Text("""")", nodeName + "_" + ObjectModelHelper.GenerateRandomID()));
                    elseActions.Add(ObjectModelHelper.SetVariableToExpression(NOINPUT_PROMPT_SPEECH_VARIABLE_NAME + retryPromptCount, @"Text("""")", nodeName + "_" + ObjectModelHelper.GenerateRandomID()));
                }
                else if (promptType.Contains("_CD_04"))
                {
                    elseActions.Add(ObjectModelHelper.SetVariableToExpression(NOMATCH_PROMPT_TEXT_VARIABLE_NAME + retryPromptCount, @"Text("""")", nodeName + "_" + ObjectModelHelper.GenerateRandomID()));
                    elseActions.Add(ObjectModelHelper.SetVariableToExpression(NOMATCH_PROMPT_SPEECH_VARIABLE_NAME + retryPromptCount, @"Text("""")", nodeName + "_" + ObjectModelHelper.GenerateRandomID()));
                }
                else
                {
                    elseActions.Add(ObjectModelHelper.SetVariableToExpression(INITIAL_PROMPT_TEXT_VARIABLE_NAME, @"Text("""")", nodeName + "_" + ObjectModelHelper.GenerateRandomID()));
                    elseActions.Add(ObjectModelHelper.SetVariableToExpression(INITIAL_PROMPT_SPEECH_VARIABLE_NAME, @"Text("""")", nodeName + "_" + ObjectModelHelper.GenerateRandomID()));
                }

                // Add concatenated expressions
                if (retryPromptCount > 0)
                {
                    elseActions.Add(ObjectModelHelper.SetVariableToExpression(textPromptVariableName + retryPromptCount, promptBuilderText.ToString(), nodeName + "_" + ObjectModelHelper.GenerateRandomID()));
                    // elseActions.Add(ObjectModelHelper.SetVariableToExpression(speechPromptVariableName + retryPromptCount, promptBuilderSpeech.ToString(), nodeName + "_" + ObjectModelHelper.GenerateRandomID()));
                    // for  metro
                    elseActions.Add(ObjectModelHelper.SetVariableToExpression(speechPromptVariableName + retryPromptCount, promptBuilderText.ToString(), nodeName + "_" + ObjectModelHelper.GenerateRandomID()));
                }
                else
                {
                    elseActions.Add(ObjectModelHelper.SetVariableToExpression(textPromptVariableName, promptBuilderText.ToString(), nodeName + "_" + ObjectModelHelper.GenerateRandomID()));
                    // elseActions.Add(ObjectModelHelper.SetVariableToExpression(speechPromptVariableName, promptBuilderSpeech.ToString(), nodeName + "_" + ObjectModelHelper.GenerateRandomID()));
                    // for metro
                    elseActions.Add(ObjectModelHelper.SetVariableToExpression(speechPromptVariableName, promptBuilderText.ToString(), nodeName + "_" + ObjectModelHelper.GenerateRandomID()));
                }
            }

        }
        /* private static bool IsVariable(string text)
         {
             // Assumes variables are alphanumeric and may include dots (e.g., Topic.message)
             return !string.IsNullOrEmpty(text) &&
                    char.IsLetter(text[0]) &&
                    text.All(c => char.IsLetterOrDigit(c) || c == '.' || c == '_');
         }*/
        private static bool IsVariable(string text)
        {
            return !string.IsNullOrEmpty(text) && text.StartsWith("Global.");
        }


        private void ProcessConditionTransitions(ConditionGroup.Builder conditionGroupBuilder, ConditionModel conditionModel, TriggerBase.Builder triggerBase, string stateName)
        {
            Console.WriteLine("ProcessConditionTransitions conditionGroup : " + conditionGroupBuilder.Id);
            var conditionItemBuilders = new List<ConditionItem.Builder>();
            var elseActions = new List<DialogAction.Builder>();

            var transition = conditionModel.Transitions;

            var IsIfCondition = conditionModel.IsIfCondition;

            if (conditionModel.Condition == "default")
            {
                HandleDefaultCondition(transition, triggerBase, stateName);
            }
            else if (conditionModel.Condition == "else")
            {
                CollectElseActions(transition, elseActions, triggerBase, stateName);
            }
            else if (transition.InnerConditionTransitions.Count > 0 || IsIfCondition)
            {
                if (IsIfCondition)
                {

                    if (transition.InnerConditionTransitions.Count > 0)
                    {
                        var conditionItemBuilder = CreateConditionItem(conditionModel.Condition, transition, triggerBase, stateName);
                        foreach (var innerCondition in transition.InnerConditionTransitions)
                        {
                            var nestedConditionGroupBuilder = new ConditionGroup.Builder
                            {
                                Id = "conditionGroup_" + ObjectModelHelper.GenerateRandomID(),
                                DisplayName = ObjectModelHelper.getLoggingStateName(stateName, "CG") //+ ObjectModelHelper.GenerateRandomID()
                            };
                            ProcessConditionTransitions(nestedConditionGroupBuilder, innerCondition, triggerBase, stateName);
                            if (nestedConditionGroupBuilder.Conditions.Any() || nestedConditionGroupBuilder.ElseActions.Any())
                            {
                                Console.WriteLine("inside if ProcessConditionTransitions conditionGroup : " + nestedConditionGroupBuilder.Id);
                                conditionItemBuilder.Actions.Add(nestedConditionGroupBuilder.Build());
                            }
                            conditionItemBuilders.Add(conditionItemBuilder);
                        }
                    }
                    /* else if (conditionModel.IsElseIfCondition == true)
                    {
                        var conditionItemBuilder = CreateConditionItem(conditionModel.Condition, transition, triggerBase, stateName);
                        conditionItemBuilders.Add(conditionItemBuilder);
                    } */
                    else
                    {
                        var conditionItemBuilder = CreateConditionItem(conditionModel.Condition, transition, triggerBase, stateName);
                        conditionItemBuilders.Add(conditionItemBuilder);
                    }
                }
                else
                {
                    var conditionItemBuilder = CreateConditionItem(conditionModel.Condition, transition, triggerBase, stateName);
                    var nestedConditionGroupBuilder = new ConditionGroup.Builder
                    {
                        Id = "conditionGroup_" + ObjectModelHelper.GenerateRandomID(),
                        DisplayName = ObjectModelHelper.getLoggingStateName(stateName, "CG") //+ ObjectModelHelper.GenerateRandomID()
                    };
                    foreach (var innerCondition in transition.InnerConditionTransitions)
                    {
                        ProcessConditionTransitions(nestedConditionGroupBuilder, innerCondition, triggerBase, stateName);
                    }
                    if (nestedConditionGroupBuilder.Conditions.Any() || nestedConditionGroupBuilder.ElseActions.Any())
                    {
                        Console.WriteLine("inside else ProcessConditionTransitions conditionGroup : " + nestedConditionGroupBuilder.Id);
                        conditionItemBuilder.Actions.Add(nestedConditionGroupBuilder.Build());
                    }
                    conditionItemBuilders.Add(conditionItemBuilder);
                }
            }
            else
            {
                var conditionItemBuilder = CreateConditionItem(conditionModel.Condition, transition, triggerBase, stateName);
                conditionItemBuilders.Add(conditionItemBuilder);
            }
            // Add condition items to the condition group builder
            foreach (var conditionItemBuilder in conditionItemBuilders)
            {
                Console.WriteLine("inside foreach conditionItemBuilders : " + conditionGroupBuilder.Id);
                conditionGroupBuilder.Conditions.Add(conditionItemBuilder.Build());
            }

            // Add collected `else` actions to the condition group builder if there are any
            if (elseActions.Count > 0)
            {
                foreach (var actionBuilder in elseActions)
                {
                    Console.WriteLine("inside foreach elseActions : " + actionBuilder.Id);
                    conditionGroupBuilder.ElseActions.Add(actionBuilder.Build());
                }
            }
        }
        private void HandleDefaultCondition(TransitionModel transitionValue, TriggerBase.Builder triggerBase, string stateName)
        {
            processSessionMapping(triggerBase, transitionValue.sessionMappings, stateName);

            if (transitionValue.PromptList.Count > 0)
            {
                StringBuilder result = new StringBuilder();
                StringBuilder speakText = new StringBuilder();


                foreach (var prompt in transitionValue.PromptList)
                {
                    speakHelper(speakText, prompt);
                }
                // Console.WriteLine("----------------------------" + speakText.ToString() + "-------------------------------------");
                foreach (var prompt in transitionValue.PromptList)
                {
                    result.Append(prompt.Text + " <br> ");
                }
                string finalString = result.ToString();
                string finalSpeakString = speakText.ToString();
                Console.WriteLine(finalSpeakString);
                MessageActivityTemplate.Builder messageActivity = new MessageActivityTemplate.Builder();
                ObjectModelHelper.AddSegmentsFromPromptToTemplateLine(finalString, messageActivity.Text);
                // ObjectModelHelper.AddSegmentsFromPromptToTemplateLine(finalSpeakString, messageActivity.Speak);
                //added for metro
                ObjectModelHelper.AddSegmentsFromPromptToTemplateLine(finalString, messageActivity.Speak);

                string messageId = "Prompt_" + ObjectModelHelper.GenerateRandomID();
                if (stateName != null)
                {
                    messageId = stateName;
                }

                SendActivity.Builder sendActivity = new SendActivity.Builder()
                {
                    Id = messageId,
                    DisplayName = messageId,
                    Activity = messageActivity
                };
                triggerBase.Actions.Add(sendActivity);
            }

            if (!string.IsNullOrEmpty(transitionValue.next))
            {
                String externalCustomStateString = CreateConcatenatedEventNameString(_customStates);
                string gotoNextNode = transitionValue.next;
                if (externalCustomStateString.Contains(gotoNextNode))
                {
                    BeginDialog.Builder redirectConversation = new BeginDialog.Builder()
                    {
                        Id = ObjectModelHelper.GenerateRandomID(),
                        Dialog = DialogExpression.Literal("topic." + gotoNextNode)

                    };
                    triggerBase.Actions.Add(redirectConversation);
                }
                /*else if (gotoNextNode.Contains("DS"))
                {
                    //LogCustomTelemetryEvent logCustomEvent = new LogCustomTelemetryEvent(gotoNextNode);
                    var logCustomEvent = new LogCustomTelemetryEvent.Builder()
                    {
                        Id = ObjectModelHelper.GenerateRandomID(),
                        EventName = gotoNextNode
                    };
                    triggerBase.Actions.Add(logCustomEvent);
                }*/
                else
                {
                    //if (gotoNextNode.EndsWith("_DM"))
                    //{
                    //   // gotoNextNode += "_CD_00";
                    //    var gotoAction = new GotoAction.Builder
                    //    {
                    //        Id = ObjectModelHelper.GenerateRandomID(),
                    //        ActionId = gotoNextNode
                    //    };
                    //    triggerBase.Actions.Add(gotoAction.Build());
                    //}
                    //else if (gotoNextNode.EndsWith("_DS"))
                    //{
                    //    gotoNextNode += "_00";
                    //    var gotoAction = new GotoAction.Builder
                    //    {
                    //        Id = ObjectModelHelper.GenerateRandomID(),
                    //        ActionId = gotoNextNode
                    //    };
                    //    triggerBase.Actions.Add(gotoAction.Build());
                    //}
                    if (gotoNextNode.EndsWith(".dvxml"))
                    {

                        gotoNextNode = gotoNextNode.Replace(".dvxml", "");
                        BeginDialog.Builder redirectDialog = new BeginDialog.Builder()
                        {
                            Id = ObjectModelHelper.GenerateRandomID(),
                            Dialog = DialogExpression.Literal("topic." + gotoNextNode)
                        };
                        triggerBase.Actions.Add(redirectDialog);

                    }
                    //else if (gotoNextNode.EndsWith("_DB") || gotoNextNode.EndsWith("_DA"))
                    //{
                    //    gotoNextNode = "DAInputs_" + gotoNextNode;
                    //    var gotoAction = new GotoAction.Builder()
                    //    {
                    //        Id = ObjectModelHelper.GenerateRandomID(),
                    //        ActionId = gotoNextNode
                    //    };
                    //    triggerBase.Actions.Add(gotoAction);
                    //}
                    //handle getreturnLink()
                    else if (gotoNextNode.EndsWith("getReturnLink()") || gotoNextNode.Equals("return"))
                    {
                        gotoNextNode = "return";
                        var gotoAction = new GotoAction.Builder()
                        {
                            Id = ObjectModelHelper.GenerateRandomID(),
                            ActionId = gotoNextNode
                        };
                        triggerBase.Actions.Add(gotoAction.Build());
                    }
                    else
                    {
                        var gotoAction = new GotoAction.Builder
                        {
                            Id = ObjectModelHelper.GenerateRandomID(),
                            ActionId = ObjectModelHelper.getLoggingStateName(gotoNextNode, "LG")
                        };
                        triggerBase.Actions.Add(gotoAction.Build());
                    }
                }
            }
        }

        private ConditionItem.Builder CreateConditionItem(string condition, TransitionModel transitionValue, TriggerBase.Builder triggerBase, string stateId)
        {
            var conditionItemBuilder = new ConditionItem.Builder
            {
                Id = "condItem_" + ObjectModelHelper.getLoggingStateName(stateId, "CD") + "_" + ObjectModelHelper.GenerateRandomID(),
                Condition = RemoveDuplicateGlobals(StateUtility.TransformCondition(condition))
            };

            if (transitionValue.sessionMappings != null)
            {
                foreach (var sessionMapping in transitionValue.sessionMappings)
                {
                    if (string.IsNullOrEmpty(sessionMapping.value))
                    {
                        conditionItemBuilder.Actions.Add(ObjectModelHelper.SetVariableToBlank("Global." + sessionMapping.key));
                    }
                    else
                    {
                        conditionItemBuilder.Actions.Add(ObjectModelHelper.SetVariable("Global." + sessionMapping.key, sessionMapping.value, sessionMapping.type, stateId));
                    }
                }
            }
            if (transitionValue.PromptList.Count > 0)
            {
                StringBuilder result = new StringBuilder();

                StringBuilder speakText = new StringBuilder();

                foreach (var prompt in transitionValue.PromptList)
                {
                    speakHelper(speakText, prompt);

                }
                //  Console.WriteLine("----------------------------" + speakText.ToString() + "-------------------------------------");
                foreach (var prompt in transitionValue.PromptList)
                {
                    result.Append(prompt.Text + " <br> ");
                }
                string finalString = result.ToString();
                String finalSpeakString = speakText.ToString();
                Console.WriteLine(finalSpeakString);
                MessageActivityTemplate.Builder messageActivity = new MessageActivityTemplate.Builder();
                ObjectModelHelper.AddSegmentsFromPromptToTemplateLine(finalString, messageActivity.Text);
                // ObjectModelHelper.AddSegmentsFromPromptToTemplateLine(finalSpeakString, messageActivity.Speak);
                // added for metro
                ObjectModelHelper.AddSegmentsFromPromptToTemplateLine(finalString, messageActivity.Speak);

                string messageId = "Prompt_" + ObjectModelHelper.GenerateRandomID();
                SendActivity.Builder sendActivity = new SendActivity.Builder()
                {
                    Id = messageId,
                    DisplayName = messageId,
                    Activity = messageActivity
                };
                conditionItemBuilder.Actions.Add(sendActivity);
            }

            if (!string.IsNullOrEmpty(transitionValue.next))
            {
                String externalCustomStateString = CreateConcatenatedEventNameString(_customStates);
                string gotoNextNode = transitionValue.next;
                if (externalCustomStateString.Contains(gotoNextNode))
                {
                    BeginDialog.Builder redirectConversation = new BeginDialog.Builder()
                    {
                        Id = ObjectModelHelper.GenerateRandomID(),
                        Dialog = DialogExpression.Literal("topic." + gotoNextNode)

                    };
                    conditionItemBuilder.Actions.Add(redirectConversation);
                }
                /* else if (gotoNextNode.Contains("DS"))
                 {
                     // LogCustomTelemetryEvent logCustomEvent = new LogCustomTelemetryEvent(gotoNextNode);
                     var logCustomEvent = new LogCustomTelemetryEvent.Builder()
                     {
                         Id = ObjectModelHelper.GenerateRandomID(),
                         EventName = gotoNextNode
                     };
                     conditionItemBuilder.Actions.Add(logCustomEvent);
                 }*/
                else
                {
                    //if (gotoNextNode.EndsWith("_DM"))
                    //{
                    //   // gotoNextNode += "_CD_00";
                    //    var gotoAction = new GotoAction.Builder
                    //    {
                    //        Id = ObjectModelHelper.GenerateRandomID(),
                    //        ActionId = gotoNextNode
                    //    };
                    //    conditionItemBuilder.Actions.Add(gotoAction.Build());
                    //}
                    //else if (gotoNextNode.EndsWith("_DS"))
                    //{
                    //    gotoNextNode += "_00";
                    //    var gotoAction = new GotoAction.Builder
                    //    {
                    //        Id = ObjectModelHelper.GenerateRandomID(),
                    //        ActionId = gotoNextNode
                    //    };
                    //    conditionItemBuilder.Actions.Add(gotoAction.Build());
                    //}
                    if (gotoNextNode.EndsWith(".dvxml"))
                    {

                        gotoNextNode = gotoNextNode.Replace(".dvxml", "");
                        BeginDialog.Builder redirectDialog = new BeginDialog.Builder()
                        {
                            Id = ObjectModelHelper.GenerateRandomID(),
                            Dialog = DialogExpression.Literal("topic." + gotoNextNode)
                        };
                        conditionItemBuilder.Actions.Add(redirectDialog);

                    }
                    //else if (gotoNextNode.EndsWith("_DB") || gotoNextNode.EndsWith("_DA"))
                    //{
                    //    gotoNextNode = "DAInputs_" + gotoNextNode;
                    //    var gotoAction = new GotoAction.Builder()
                    //    {
                    //        Id = ObjectModelHelper.GenerateRandomID(),
                    //        ActionId = gotoNextNode
                    //    };
                    //    conditionItemBuilder.Actions.Add(gotoAction);
                    //}
                    //Handle getReturnLink()
                    else if (gotoNextNode.EndsWith("getReturnLink()") || gotoNextNode.Equals("return"))
                    {
                        gotoNextNode = "return";
                        var gotoAction = new GotoAction.Builder()
                        {
                            Id = ObjectModelHelper.GenerateRandomID(),
                            ActionId = gotoNextNode
                        };
                        conditionItemBuilder.Actions.Add(gotoAction);
                    }
                    else
                    {
                        var gotoAction = new GotoAction.Builder
                        {
                            Id = ObjectModelHelper.GenerateRandomID(),
                            ActionId = ObjectModelHelper.getLoggingStateName(gotoNextNode, "LG")
                        };
                        conditionItemBuilder.Actions.Add(gotoAction.Build());
                    }

                }
            }

            return conditionItemBuilder;
        }

        private void CollectElseActions(TransitionModel transitionValue, List<DialogAction.Builder> elseActions, TriggerBase.Builder triggerBase, string stateId)
        {
            if (transitionValue.InnerConditionTransitions.Count > 0)
            {
                var nestedElseIfConditionGroupBuilder = new ConditionGroup.Builder
                {
                    Id = "conditionGroup_" + ObjectModelHelper.GenerateRandomID(),
                    DisplayName = ObjectModelHelper.getLoggingStateName(stateId, "CG") //+ ObjectModelHelper.GenerateRandomID()
                };
                // Create a nested condition group for inner conditions
                var nestedConditionGroupBuilder = new ConditionGroup.Builder
                {
                    Id = "conditionGroup_" + ObjectModelHelper.GenerateRandomID(),
                    DisplayName = ObjectModelHelper.getLoggingStateName(stateId, "CG") //+ ObjectModelHelper.GenerateRandomID()
                };

                for (int i = 0; i < transitionValue.InnerConditionTransitions.Count; i++)
                {
                    var innerCondition = transitionValue.InnerConditionTransitions[i];
                    if (i > 0)
                    {
                        ProcessConditionTransitions(nestedElseIfConditionGroupBuilder, innerCondition, triggerBase, stateId);
                    }
                    else
                    {
                        ProcessConditionTransitions(nestedConditionGroupBuilder, innerCondition, triggerBase, stateId);
                    }
                }

                // Add the nested condition group to the condition item actions
                if (nestedConditionGroupBuilder.Conditions.Any() || nestedConditionGroupBuilder.ElseActions.Any())
                {
                    elseActions.Add(nestedConditionGroupBuilder.Build());
                }
                if (nestedElseIfConditionGroupBuilder.Conditions.Any() || nestedElseIfConditionGroupBuilder.ElseActions.Any())
                {
                    elseActions.Add(nestedElseIfConditionGroupBuilder.Build());
                }

            }
            if (transitionValue.sessionMappings != null)
            {
                foreach (var sessionMapping in transitionValue.sessionMappings)
                {
                    if (string.IsNullOrEmpty(sessionMapping.value))
                    {
                        elseActions.Add(ObjectModelHelper.SetVariableToBlank("Global." + sessionMapping.key));
                    }
                    else
                    {
                        elseActions.Add(ObjectModelHelper.SetVariable("Global." + sessionMapping.key, sessionMapping.value, sessionMapping.type, stateId));
                    }
                }
            }
            if (transitionValue.PromptList.Count > 0)
            {
                StringBuilder result = new StringBuilder();
                StringBuilder speakText = new StringBuilder();

                foreach (var prompt in transitionValue.PromptList)
                {
                    speakHelper(speakText, prompt);
                }
                //Console.WriteLine("----------------------------"+speakText.ToString()+"-------------------------------------");
                foreach (var prompt in transitionValue.PromptList)
                {
                    result.Append(prompt.Text + " <br> ");
                }
                string finalString = result.ToString();
                string finalSpeakString = speakText.ToString();
                Console.WriteLine(finalSpeakString);
                MessageActivityTemplate.Builder messageActivity = new MessageActivityTemplate.Builder();
                ObjectModelHelper.AddSegmentsFromPromptToTemplateLine(finalString, messageActivity.Text);
                // ObjectModelHelper.AddSegmentsFromPromptToTemplateLine(finalSpeakString, messageActivity.Speak);
                // added for metro
                ObjectModelHelper.AddSegmentsFromPromptToTemplateLine(finalString, messageActivity.Speak);

                string messageId = "Prompt_" + ObjectModelHelper.GenerateRandomID();
                SendActivity.Builder sendActivity = new SendActivity.Builder()
                {
                    Id = messageId,
                    DisplayName = messageId,
                    Activity = messageActivity
                };
                elseActions.Add(sendActivity);
            }

            if (!string.IsNullOrEmpty(transitionValue.next))
            {
                String externalCustomStateString = CreateConcatenatedEventNameString(_customStates);
                string gotoNextNode = transitionValue.next;
                if (externalCustomStateString.Contains(gotoNextNode))
                {
                    BeginDialog.Builder redirectConversation = new BeginDialog.Builder()
                    {
                        Id = ObjectModelHelper.GenerateRandomID(),
                        Dialog = DialogExpression.Literal("topic." + gotoNextNode)

                    };
                    elseActions.Add(redirectConversation);
                }
                else
                {
                    if (gotoNextNode.EndsWith(".dvxml"))
                    {
                        gotoNextNode = gotoNextNode.Replace(".dvxml", "");
                        BeginDialog.Builder redirectConversation = new BeginDialog.Builder()
                        {
                            Id = ObjectModelHelper.GenerateRandomID(),
                            Dialog = DialogExpression.Literal("topic." + gotoNextNode)
                        };
                        elseActions.Add(redirectConversation);
                    }

                    else if (gotoNextNode.EndsWith("getReturnLink()") || gotoNextNode.Equals("return"))
                    {
                        gotoNextNode = "return";
                        var gotoAction = new GotoAction.Builder()
                        {
                            Id = ObjectModelHelper.GenerateRandomID(),
                            ActionId = gotoNextNode
                        };
                        elseActions.Add(gotoAction);
                    }
                    else
                    {
                        var gotoAction = new GotoAction.Builder
                        {
                            Id = ObjectModelHelper.GenerateRandomID(),
                            ActionId = ObjectModelHelper.getLoggingStateName(gotoNextNode, "LG")
                        };
                        elseActions.Add(gotoAction.Build());
                    }
                }
            }
        }
        private void speakHelper(StringBuilder speakString, PromptModel model)
        {
            string promptId = model.PromptId;
            // Check if the text is plain or dynamic
            string formattedText = model.Text.Contains(".Global")
                ? model.Text // Dynamic reference
                : model.Text; // Plain text


            string var = "<br>Global.audioBaseUrl<br>";
            StringBuilder audiotag = new StringBuilder($"<audio src=\"");

            // Check conditions and append appropriately
            if (model.Text.Contains("Global.") || model.PromptId == "custom_prompt_id" || model.PromptId == "customPrompt")
            {
                // Append text as is
                speakString.Append($"{formattedText}");
            }
            else
            {
                // Append the full audio tag with formatted text
                speakString.Append($"{audiotag}{var}/{promptId}.wav\"> {formattedText} </audio>");

                // Optionally, add a line break if required
                speakString.Append(" <br>");
            }

        }
        private void processSessionMapping(TriggerBase.Builder beginDialog, List<SessionMappingModel> sessionMappings, String stateName)
        {
            if (sessionMappings != null)
            {
                foreach (var sessionMapping in sessionMappings)
                {
                    if (sessionMapping.value == null || sessionMapping.value == "")
                    {
                        beginDialog.Actions.Add(ObjectModelHelper.SetVariableToBlank("Global." + sessionMapping.key));
                    }
                    else if (sessionMapping.key == "clear_variable_values")
                    {
                        var clearVariables = new ClearAllVariables.Builder
                        {
                            Id = ObjectModelHelper.GenerateRandomID(),
                        };
                        beginDialog.Actions.Add(clearVariables.Build());
                    }
                    else if (sessionMapping.key == "CreateBackendSessionRequest")
                    {
                        HttpRequestAction.Builder httpRequestAction = new HttpRequestAction.Builder()
                        {
                            Id = "CreateSession_" + ObjectModelHelper.GenerateRandomID(),
                            DisplayName = "CreateBackendSession",
                            Method = HttpMethodTypeWrapper.Get(HttpMethodType.Get),
                            Url = "=Global.sessionUrl",
                            Body = { },
                            Response = InitializablePropertyPath.Create("Global." + "backendSessionId"),
                        };
                        beginDialog.Actions.Add(httpRequestAction.Build());
                    }
                    else if (sessionMapping.key == "CreatInitVarsRequest")
                    {
                        HttpRequestAction.Builder httpRequestAction = new HttpRequestAction.Builder()
                        {
                            Id = "SetSession_" + ObjectModelHelper.GenerateRandomID(),
                            DisplayName = "SetSession",
                            Method = HttpMethodTypeWrapper.Get(HttpMethodType.Post),
                            Url = "=Global.initSessionUrl",
                            Body = { },
                            Response = InitializablePropertyPath.Create("Global." + "initVars"),
                        };
                        httpRequestAction.Headers.Add("Content-Type", "application/json");
                        httpRequestAction.Headers.Add("Authorization", "Basic YWRtaW46YWRtaW4xMjM=");
                        httpRequestAction.Headers.Add("Cookie", "=Concatenate(\"SESSION=\",Global.backendSessionId)");
                        httpRequestAction.Headers.Add("password", "=Global.password");
                        httpRequestAction.Headers.Add("username", "=Global.username");
                        beginDialog.Actions.Add(httpRequestAction.Build());
                    }
                    else if (sessionMapping.key == "audioBaseUrl")
                    {
                        String value = "Global.AudiobaseUrlStart & Global.projectName & \"/\" & Global.audioLanguage & Global.AudiobaseUrlEnd";
                        beginDialog.Actions.Add(ObjectModelHelper.SetVariableToExpression("Global." + sessionMapping.key, value));
                    }
                    else
                    {
                        beginDialog.Actions.Add(ObjectModelHelper.SetVariable("Global." + sessionMapping.key, sessionMapping.value, sessionMapping.type, stateName));
                    }

                }
            }
        }
        public static string RemoveDuplicateGlobals(string input)
        {
            // Use a regular expression to identify tokens while keeping logical operators and special characters intact
            var tokenRegex = new System.Text.RegularExpressions.Regex(@"(\bGlobal\.[\w.]+|\|\||&&|[!()=]|"".*?""|\s+|\S)");

            // Use a StringBuilder to reconstruct the result
            StringBuilder result = new StringBuilder();

            foreach (System.Text.RegularExpressions.Match match in tokenRegex.Matches(input))
            {
                string token = match.Value;

                if (token.StartsWith("Global."))
                {
                    // Process tokens starting with "Global."
                    result.Append(RemoveRedundantGlobal(token));
                }
                else
                {
                    // Append other parts (logical operators, spaces, etc.) as-is
                    result.Append(token);
                }
            }

            // Ensure single-quoted strings inside double quotes are corrected
            string finalResult = result.ToString();
            finalResult = RemoveSingleQuotesInsideDoubleQuotes(finalResult);
            if (finalResult.Contains("undefined") || finalResult.Contains("null"))
            {
                finalResult = finalResult.Replace("undefined.Global.", "=Blank()");
                finalResult = finalResult.Replace("undefined", "=Blank()");
                finalResult = finalResult.Replace("=undefined", "=Blank()");
                finalResult = finalResult.Replace("null.Global.", "");
            }

            return finalResult;
        }
        //public static void UpdateUndefinedValues(object yamlData)
        //{
        //    if (yamlData is List<object> list)
        //    {
        //        foreach (var item in list)
        //        {
        //            UpdateUndefinedValues(item);
        //        }
        //    }
        //    else if (yamlData is Dictionary<string, object> dict)
        //    {
        //        foreach (var key in new List<string>(dict.Keys))
        //        {
        //            if (dict[key] is string value)
        //            {
        //                // Convert conditions like A != undefined to !isBlank(A)
        //                dict[key] = ConvertCondition(value);
        //            }
        //            else
        //            {
        //                UpdateUndefinedValues(dict[key]);
        //            }
        //        }
        //    }
        //}

        //public static string ConvertCondition(string condition)
        //{
        //    // Match conditions like A != undefined or A <>> undefined
        //    var negationPatterns = new List<string>
        //{
        //    @"(\b[\w\.]+\b) != undefined",
        //    @"(\b[\w\.]+\b) <> undefined",
        //    @"(\b[\w\.]+\b) <> ""undefined""",
        //    @"(\b[\w\.]+\b) != ""undefined""",
        //    @"(\b[\w\.]+\b) =undefined",
        //    @"(\b[\w\.]+\b) !=undefined",
        //    @"(\b[\w\.]+\b) != undefined",
        //    @"(\b[\w\.]+\b)!=undefined",
        //    @"(\b[\w\.]+\b)<>undefined",
        //    @"(\b[\w\.]+\b\.typeof\([\w\.]+\)) <> ""undefined""",
        //    @"(\b[\w\.]+\b\.typeof\([\w\.]+\)) !=undefined",
        //    @"(\b[\w\.]+\b) != 'undefined'",
        //    @"(\b[\w\.]+\b) <> 'undefined'",
        //    @"(\b[\w\.]+\b\.typeof\([\w\.]+\)) <> 'undefined'",
        //    @"(\b[\w\.]+\b\.typeof\([\w\.]+\)) != 'undefined'"
        //};

        //    var equalPatterns = new List<string>
        //{
        //    @"(\b[\w\.]+\b) == undefined",
        //    @"(\b[\w\.]+\b) = undefined",
        //    @"(\b[\w\.]+\b) == ""undefined""",
        //    @"(\b[\w\.]+\b) = ""undefined""",
        //   // @"(\b[\w\.]+\b) =="undefined"",
        //    @"(\b[\w\.]+\b) =undefined",
        //    @"(\b[\w\.]+\b) =""undefined""",
        //    @"(\b[\w\.]+\b)==undefined",
        //    @"(\b[\w\.]+\b)=undefined",
        //    @"(\b[\w\.]+\b\.typeof\([\w\.]+\)) == undefined""",
        //    @"(\b[\w\.]+\b\.typeof\([\w\.]+\)) == undefined",
        //    @"(\b[\w\.]+\b\.typeof\([\w\.]+\)) = ""undefined""",
        //    @"(\b[\w\.]+\b\.typeof\([\w\.]+\)) = undefined",
        //    @"(\b[\w\.]+\b\.typeof\([\w\.]+\)) =undefined",
        //    @"(\b[\w\.]+\b\.typeof\([\w\.]+\)) =""undefined""",
        //    @"(\b[\w\.]+\b\.typeof\([\w\.]+\)) =""undefined",
        //    @"(\b[\w\.]+\b) == 'undefined'",
        //    @"(\b[\w\.]+\b) = 'undefined'",
        //    @"(\b[\w\.]+\b\.typeof\([\w\.]+\)) == 'undefined'",
        //    @"(\b[\w\.]+\b\.typeof\([\w\.]+\)) = 'undefined'",
        //    @"(\b[\w\.]+\b) : undefined"
        //};

        //    var blankPatterns = new List<string>
        //{
        //    @"=\s*undefined",
        //    @"=\s*undefined\s*",
        //    @"=\s*undefined\s*;",
        //    @"=\s*undefined\s*\)",
        //    @"=\s*undefined\s*,",
        //    @"=\s*undefined\s*\n",
        //    @"=\s*undefined\s*$",
        //    @"=\s*undefined\s*\r\n",
        //    @"=\s undefined\s*\r\n",
        //    @"value:\s*undefined"
        //};

        //    // Replace negation patterns with !isBlank
        //    foreach (var pattern in negationPatterns)
        //    {
        //        condition = Regex.Replace(condition, pattern, "!isBlank($1)");
        //    }

        //    // Replace equality patterns with isBlank
        //    foreach (var pattern in equalPatterns)
        //    {
        //        condition = Regex.Replace(condition, pattern, "isBlank($1)");
        //    }

        //    // Replace blank patterns with =Blank()
        //    foreach (var pattern in blankPatterns)
        //    {
        //        condition = Regex.Replace(condition, "=\\s*undefined", "=Blank()");
        //    }

        //    return condition;
        //}
        private static string RemoveRedundantGlobal(string token)
        {
            // Split the token by "."
            string[] parts = token.Split('.');

            // Use a flag to track whether "Global" has been encountered
            List<string> resultParts = new List<string>();
            bool foundGlobal = false;

            foreach (string part in parts)
            {
                if (part == "Global")
                {
                    if (!foundGlobal)
                    {
                        foundGlobal = true; // Keep the first "Global"
                        resultParts.Add(part);
                    }
                }
                else
                {
                    resultParts.Add(part);
                }
            }

            return string.Join(".", resultParts);
        }

        private static string RemoveSingleQuotesInsideDoubleQuotes(string input)
        {
            if (input != null && input.Contains("')\""))
            {
                //(Global.GlobalVars.tag = "'inquire-balance')"
                // <if cond = "(GlobalVars.tag == 'inquire-balance')" >

                input = input.Replace("'", "");
                for (int i = 0; i < input.Length - 1; i++)
                {
                    // Check if current character is a closing quote and the next is a closing parenthesis
                    if (input[i] == ')' && input[i + 1] == '"')
                    {
                        // Swap `)` and `"` at position i and i+1
                        char[] chars = input.ToCharArray();
                        char temp = chars[i];
                        chars[i] = chars[i + 1];
                        chars[i + 1] = temp;

                        // Convert the char array back to a string
                        input = new string(chars);

                        // Break after swapping, as we only want to perform this swap once
                        break;
                    }
                }

            }
            // This regex removes single quotes from the start and end of strings inside double quotes
            return System.Text.RegularExpressions.Regex.Replace(input, @"\""(.*?)\'(.*?)\'\""", "\"$1$2\"");

        }

        private static string getNextNodeForCondition(ConditionItem.Builder conditionItemBuilder, string gotoNextNode)
        {
            //if (gotoNextNode.EndsWith("_DM"))
            //{
            //   // gotoNextNode += "_CD_00";
            //    var gotoAction = new GotoAction.Builder
            //    {
            //        Id = ObjectModelHelper.GenerateRandomID(),
            //        ActionId = gotoNextNode
            //    };
            //    conditionItemBuilder.Actions.Add(gotoAction.Build());
            //}
            //else if (gotoNextNode.EndsWith("_DS"))
            //{
            //    gotoNextNode += "_00";
            //    var gotoAction = new GotoAction.Builder
            //    {
            //        Id = ObjectModelHelper.GenerateRandomID(),
            //        ActionId = gotoNextNode
            //    };
            //    conditionItemBuilder.Actions.Add(gotoAction.Build());
            //}
            if (gotoNextNode.EndsWith(".dvxml"))
            {
                gotoNextNode = gotoNextNode.Replace(".dvxml", "");
                BeginDialog.Builder redirectDialog = new BeginDialog.Builder()
                {
                    Id = ObjectModelHelper.GenerateRandomID(),
                    Dialog = DialogExpression.Literal("topic." + gotoNextNode)
                };
                conditionItemBuilder.Actions.Add(redirectDialog);

            }
            //else if (gotoNextNode.EndsWith("_DB") || gotoNextNode.EndsWith("_DA"))
            //{
            //    gotoNextNode = "DAInputs_" + gotoNextNode;
            //    var gotoAction = new GotoAction.Builder()
            //    {
            //        Id = ObjectModelHelper.GenerateRandomID(),
            //        ActionId = gotoNextNode
            //    };
            //    conditionItemBuilder.Actions.Add(gotoAction);
            //}
            else if (gotoNextNode.EndsWith("getReturnLink()") || gotoNextNode.Equals("return"))
            {
                gotoNextNode = "return";
                var gotoAction = new GotoAction.Builder()
                {
                    Id = ObjectModelHelper.GenerateRandomID(),
                    ActionId = gotoNextNode
                };
                conditionItemBuilder.Actions.Add(gotoAction);
            }
            else
            {
                var gotoAction = new GotoAction.Builder
                {
                    Id = ObjectModelHelper.GenerateRandomID(),
                    ActionId = ObjectModelHelper.getLoggingStateName(gotoNextNode, "LG")
                };
                conditionItemBuilder.Actions.Add(gotoAction.Build());
            }

            return gotoNextNode;
        }

        private static string getNextNodeForNoCondition(TriggerBase.Builder triggerBase, string gotoNextNode)
        {
            if (gotoNextNode.EndsWith(".dvxml"))
            {
                gotoNextNode = gotoNextNode.Replace(".dvxml", "");
                BeginDialog.Builder redirectDialog = new BeginDialog.Builder()
                {
                    Id = ObjectModelHelper.GenerateRandomID(),
                    Dialog = DialogExpression.Literal("topic." + gotoNextNode)
                };
                triggerBase.Actions.Add(redirectDialog);

            }

            else if (gotoNextNode.EndsWith("getReturnLink()") || gotoNextNode.Equals("return"))
            {
                gotoNextNode = "return";
                var gotoAction = new GotoAction.Builder()
                {
                    Id = ObjectModelHelper.GenerateRandomID(),
                    ActionId = gotoNextNode
                };
                triggerBase.Actions.Add(gotoAction);
            }
            else
            {
                var gotoAction = new GotoAction.Builder
                {
                    Id = ObjectModelHelper.GenerateRandomID(),
                    ActionId = ObjectModelHelper.getLoggingStateName(gotoNextNode, "LG")
                };
                triggerBase.Actions.Add(gotoAction.Build());
            }

            return gotoNextNode;
        }


        private static string getNextNodeForElseAction(List<DialogAction.Builder> elseAction, string gotoNextNode)
        {
            //if (gotoNextNode.EndsWith("_DM"))
            //{
            //    //gotoNextNode = gotoNextNode + "_CD_00";
            //    var gotoAction = new GotoAction.Builder()
            //    {
            //        Id = ObjectModelHelper.GenerateRandomID(),
            //        ActionId = gotoNextNode
            //    };
            //    elseAction.Add(gotoAction);
            //}
            //else if (gotoNextNode.EndsWith("_DS"))
            //{
            //    gotoNextNode += "_00";
            //    var gotoAction = new GotoAction.Builder()
            //    {
            //        Id = ObjectModelHelper.GenerateRandomID(),
            //        ActionId = gotoNextNode
            //    };
            //    elseAction.Add(gotoAction);
            //}
            if (gotoNextNode.EndsWith(".dvxml"))
            {
                gotoNextNode = gotoNextNode.Replace(".dvxml", "");
                BeginDialog.Builder redirectDialog = new BeginDialog.Builder()
                {
                    Id = ObjectModelHelper.GenerateRandomID(),
                    Dialog = DialogExpression.Literal("topic." + gotoNextNode)
                };
                elseAction.Add(redirectDialog);

            }
            //else if (gotoNextNode.EndsWith("_DB") || gotoNextNode.EndsWith("_DA"))
            //{
            //    gotoNextNode = "DAInputs_" + gotoNextNode;
            //    var gotoAction = new GotoAction.Builder()
            //    {
            //        Id = ObjectModelHelper.GenerateRandomID(),
            //        ActionId = gotoNextNode
            //    };
            //    elseAction.Add(gotoAction);
            //}
            else if (gotoNextNode.EndsWith("getReturnLink()") || gotoNextNode.Equals("return"))
            {
                gotoNextNode = "return";
                var gotoAction = new GotoAction.Builder()
                {
                    Id = ObjectModelHelper.GenerateRandomID(),
                    ActionId = gotoNextNode
                };
                elseAction.Add(gotoAction);
            }
            else
            {
                var gotoAction = new GotoAction.Builder
                {
                    Id = ObjectModelHelper.GenerateRandomID(),
                    ActionId = ObjectModelHelper.getLoggingStateName(gotoNextNode, "LG")
                };
                elseAction.Add(gotoAction.Build());
            }

            return gotoNextNode;
        }

        //public static string getLoggingStateName(string id)
        //{
        //    if (string.IsNullOrEmpty(id))
        //        return id;

        //    // Pattern matches only the suffix at the end, not digits or underscores after
        //    string pattern = "(_SD|_DM|_DS|_PP|_DA|_DB)$";
        //    id = Regex.Replace(id, pattern, "", RegexOptions.IgnoreCase);
        //    return id + "_LG";
        //}

        /// <summary>
        /// Gets entity variables with external grammar integration (matches reference pattern)
        /// Checks external grammar entities first, then falls back to standard entity resolution
        /// </summary>
        /// <param name="questionNode">The question node</param>
        /// <param name="nonCluCopilotStudioEntity">Output: External grammar entity if found</param>
        /// <param name="entityName">Output: Resolved entity name</param>
        /// <param name="entityVariableName">Output: Entity variable name</param>
    }
}
