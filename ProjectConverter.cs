using Microsoft.Bot.ObjectModel;
using System.ComponentModel;
using System.Threading.Channels;
using Microsoft.PS.Tools;
using Google.Protobuf.WellKnownTypes;
using NUnit.Framework.Constraints;

namespace NDFToCopilotStudioConverter
{
    public class ProjectConverter
    {
        private DialogList _project;
        private static readonly string BasePath = "./";
        Dictionary<string, CopilotStudioEntity> _externalGrammarToEntityMap;
        private Dictionary<string, string> _allGrammarNameToEntityMappings;
        private static readonly string SamplesPath = Path.Combine(BasePath, "Samples", "Metro");
        private static readonly string ExternalGrammarJsonPath = Path.Combine(SamplesPath, "ExternalGrammarEntityMapping_112348.json");
        private FeatureFlagOptions _featureFlags;

        public ProjectConverter(DialogList project, FeatureFlagOptions featureFlags)
        {
            _project = project;
            _featureFlags = featureFlags;
            _externalGrammarToEntityMap = new Dictionary<string, CopilotStudioEntity>();
            _allGrammarNameToEntityMappings = new Dictionary<string, string>();
        }

        public BotDefinition ConvertToBotDefinition()
        {
            BotDefinition.Builder botDefinitionBuilder = new BotDefinition.Builder();
            BotEntity.Builder botEntityBuilder = new BotEntity.Builder();

            botEntityBuilder.DisplayName = "Sample_Bot";

            //TODO: channels
            BotConfiguration.Builder botConfigurationBuilder = new BotConfiguration.Builder();
            ChannelDefinition.Builder channelDefinitionBuilder = new ChannelDefinition.Builder()
            {
                ChannelId = new ChannelId(),
                Id = "Telephony",
                ChannelSpecifier = "Telephony",   // TODO: Check if this is correct
                DisplayName = "Telephony"
            };
            botConfigurationBuilder.Channels.Add(channelDefinitionBuilder);
            botConfigurationBuilder.IsTelephonyEnabled = true;

            Guid botGuid = Guid.NewGuid();
            botEntityBuilder.CdsBotId = new BotEntityId(botGuid);
            // botEntityBuilder.SchemaName = "TestBot_" + ObjectModelHelper.GenerateRandomID();

            botEntityBuilder.ManagedProperties = new ManagedProperties.Builder()
            {
                IsCustomizable = false
            };

            //components
            foreach (var component in _project.dialogModels)
            {
                //for metro
                //if (component == null || component.dialogName.StartsWith("declareVariables_") || component.dialogName.Equals("BackendInitialization"))
                if (component == null || (component.dialogName.Equals("BackendInitialization") && _featureFlags.IsPlaceholderDA))
                {
                    continue; // Skip null or empty components
                }
                ComponentConverter componentConverter = new ComponentConverter(component, botGuid, _project.customStates, _featureFlags);

                // Option 1: You can modify ComponentConverter to accept additional configuration
                // For example, you could add a method to set default entity behavior:
                // componentConverter.SetDefaultEntityBehavior(true); // This would force StringPrebuiltEntity as default

                var dialogComponent = componentConverter.ConvertToDialogComponent();
                botDefinitionBuilder.Components.Add(componentConverter.ConvertToDialogComponent());

                // Option 2: Post-process the component after it's created but before it's added
                // You could modify the component here if needed
                // PostProcessDialogComponent(dialogComponent);

                // Collect grammar mappings from this component
                var componentGrammarMappings = componentConverter.GetGrammarNameToEntityMap();
                Console.WriteLine($"[ProjectConverter] Component '{component.dialogName}' grammar mappings count: {componentGrammarMappings.Count}");

                foreach (var mapping in componentGrammarMappings)
                {
                    Console.WriteLine($"[ProjectConverter] Found grammar mapping: {mapping.Key} -> {mapping.Value}");
                    if (!_allGrammarNameToEntityMappings.ContainsKey(mapping.Key))
                    {
                        _allGrammarNameToEntityMappings[mapping.Key] = mapping.Value;
                        Console.WriteLine($"[ProjectConverter] ✓ Added grammar mapping: {mapping.Key} -> {mapping.Value}");
                    }
                    else
                    {
                        Console.WriteLine($"[ProjectConverter] ⚠ Duplicate grammar mapping skipped: {mapping.Key} -> {mapping.Value}");
                    }
                }
            }
            foreach (var customState in _project.customStates)
            {
                CustomStateConvertor componentConverter = new CustomStateConvertor(customState, botGuid);
                botDefinitionBuilder.Components.Add(componentConverter.ConvertToDialogComponent());
            }

            botConfigurationBuilder.Build();
            botEntityBuilder.Configuration = botConfigurationBuilder.Build();
            botEntityBuilder.Build();
            botDefinitionBuilder.Entity = botEntityBuilder.Build();

            // Now that we have all grammar mappings, convert them to entities
            Console.WriteLine($"Total grammar mappings collected: {_allGrammarNameToEntityMappings.Count}");
            _externalGrammarToEntityMap = ConvertExternalGrammarJsonToMap(_project, _allGrammarNameToEntityMappings);

            var customEntitiesFromExternalGrammars = ExternalGrammarConverter.GetUniqueCustomEntities(_externalGrammarToEntityMap);
            Console.WriteLine($"Total custom entities from external grammars: {customEntitiesFromExternalGrammars.Count}");
            foreach (var entity in customEntitiesFromExternalGrammars)
            {
                // Set the ParentBotId for the entity before adding it to the bot definition
                if (entity is CustomEntityComponent customEntity)
                {
                    // Create a new builder from the existing entity to modify it
                    var entityBuilder = customEntity.ToBuilder();
                    entityBuilder.ParentBotId = botGuid;
                    entityBuilder.SchemaName = "topic." + customEntity.DisplayName;
                    entityBuilder.Description = "Custom entity from external grammar";
                    entityBuilder.PublisherUniqueName = "DefaultPublisherorgeab55a1a";
                    entityBuilder.Id = Guid.NewGuid();
                    entityBuilder.ShareContext = new ContentShareContext.Builder();

                    // Build the updated entity and add it to the bot definition
                    var updatedEntity = entityBuilder.Build();
                    botDefinitionBuilder.Components.Add(updatedEntity);
                    Console.WriteLine($"[ProjectConverter] Added custom entity from external grammar with botId: {updatedEntity.DisplayName}");
                }
                else
                {
                    // If it's not a CustomEntityComponent, add it as-is (fallback)
                    botDefinitionBuilder.Components.Add(entity);
                    Console.WriteLine($"[ProjectConverter] Added entity from external grammar (non-CustomEntityComponent): {entity.DisplayName}");
                }
            }

            // Now that all components and entities are added, post-process to update question entities
            // with better entities from external grammar mappings (except for specific prebuilt entities)
            PostProcessQuestionEntities(botDefinitionBuilder);

            return botDefinitionBuilder.Build();
        }

        // Method to get all collected grammar name to entity mappings
        public Dictionary<string, string> GetAllGrammarNameToEntityMappings()
        {
            return _allGrammarNameToEntityMappings;
        }

        /// <summary>
        /// Post-processes components to set default StringPrebuiltEntity for questions that need it
        /// This method can be called after botDefinitionBuilder.Components.Add(entity) to modify entities
        /// </summary>
        /// <param name="botDefinitionBuilder">The bot definition builder containing components to process</param>
        private void PostProcessComponentEntities(BotDefinition.Builder botDefinitionBuilder)
        {
            // This is where you can implement logic to modify question entities after components are added
            // For example, you could iterate through components and find questions that need StringPrebuiltEntity

            foreach (var component in botDefinitionBuilder.Components)
            {
                // Check if this is a DialogComponent.Builder
                if (component is DialogComponent.Builder dialogComponentBuilder)
                {
                    // Process the dialog component to find and modify question entities
                    ProcessDialogComponentEntities(dialogComponentBuilder);
                }
            }
        }

        /// <summary>
        /// Post-processes all components to find questions with replaceable entities and update them
        /// with better entities from external grammar mappings if available
        /// Skips questions with ZipCode, PhoneNumber, Number, and Money prebuilt entities
        /// This is called after all components and external grammar entities are added to the bot
        /// </summary>
        /// <param name="botDefinitionBuilder">The bot definition builder containing all components</param>
        private void PostProcessQuestionEntities(BotDefinition.Builder botDefinitionBuilder)
        {
            Console.WriteLine($"[ProjectConverter] Starting post-processing of question entities...");
            Console.WriteLine($"[ProjectConverter] Available external grammar entities: {_externalGrammarToEntityMap.Count}");
            Console.WriteLine($"[ProjectConverter] Available grammar mappings: {_allGrammarNameToEntityMappings.Count}");

            int updatedQuestionsCount = 0;

            foreach (var component in botDefinitionBuilder.Components)
            {
                if (component is DialogComponent.Builder dialogComponentBuilder)
                {
                    // Process this dialog component to find and update question entities
                    int componentUpdates = ProcessDialogComponentForEntityReplacement(dialogComponentBuilder);
                    updatedQuestionsCount += componentUpdates;
                }
            }

            Console.WriteLine($"[ProjectConverter] Post-processing complete. Updated {updatedQuestionsCount} questions with better entities.");
        }





        /// <summary>
        /// Processes a single dialog component to find questions with replaceable entities
        /// and update them with better entities from external grammar mappings
        /// Skips dialogs that start with ConversationStart, declareVariables_, or BackendInitialization
        /// </summary>
        /// <param name="dialogComponentBuilder">The dialog component to process</param>
        /// <returns>Number of questions updated in this component</returns>
        private int ProcessDialogComponentForEntityReplacement(DialogComponent.Builder dialogComponentBuilder)
        {
            string dialogName = dialogComponentBuilder.DisplayName ?? "Unknown";
            Console.WriteLine($"[ProjectConverter] Processing dialog component: {dialogName}");

            // Skip dialogs that start with specific prefixes
            if (dialogName.StartsWith("ConversationStart") ||
                dialogName.StartsWith("declareVariables_") ||
                dialogName.StartsWith("BackendInitialization"))
            {
                Console.WriteLine($"[ProjectConverter] Skipping dialog '{dialogName}' - matches exclusion criteria");
                return 0;
            }

            int updatedCount = 0;

            try
            {
                // Access the dialog structure: DialogComponent -> Dialog (AdaptiveDialog) -> BeginDialog (TriggerBase) -> Actions
                if (dialogComponentBuilder.Dialog is AdaptiveDialog.Builder adaptiveDialogBuilder)
                {
                    if (adaptiveDialogBuilder.BeginDialog is TriggerBase.Builder triggerBaseBuilder)
                    {
                        // Traverse through all actions in the trigger base to find Question actions
                        updatedCount += ProcessActionsForEntityReplacement(triggerBaseBuilder.Actions, dialogName);
                    }
                    else
                    {
                        Console.WriteLine($"[ProjectConverter] Warning: BeginDialog is not TriggerBase.Builder in {dialogName}");
                    }
                }
                else
                {
                    Console.WriteLine($"[ProjectConverter] Warning: Dialog is not AdaptiveDialog.Builder in {dialogName}");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"[ProjectConverter] Error processing dialog component {dialogName}: {ex.Message}");
            }

            return updatedCount;
        }

        /// <summary>
        /// Recursively processes a collection of actions to find and update Question actions with replaceable entities
        /// </summary>
        /// <param name="actionScope">The action scope containing actions to process</param>
        /// <param name="componentName">Name of the component for logging</param>
        /// <returns>Number of questions updated</returns>
        private int ProcessActionsForEntityReplacement(ActionScope.Builder actionScope, string componentName)
        {
            int updatedCount = 0;

            if (actionScope?.Actions == null) return updatedCount;

            foreach (var action in actionScope.Actions)
            {
                try
                {
                    // Check if this action is a Question
                    if (action is Question.Builder questionBuilder)
                    {
                        updatedCount += ProcessQuestionForEntityReplacement(questionBuilder, componentName);
                    }
                    // Check for ConditionGroup which may contain nested actions
                    else if (action is ConditionGroup.Builder conditionGroupBuilder)
                    {
                        updatedCount += ProcessConditionGroupForEntityReplacement(conditionGroupBuilder, componentName);
                    }
                    // Add other action types that might contain nested actions as needed
                    // For example: BeginDialog, etc.
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"[ProjectConverter] Error processing action in {componentName}: {ex.Message}");
                }
            }

            return updatedCount;
        }

        /// <summary>
        /// Processes a ConditionGroup to find questions with replaceable entities in its conditions and else actions
        /// </summary>
        /// <param name="conditionGroupBuilder">The condition group to process</param>
        /// <param name="componentName">Name of the component for logging</param>
        /// <returns>Number of questions updated</returns>
        private int ProcessConditionGroupForEntityReplacement(ConditionGroup.Builder conditionGroupBuilder, string componentName)
        {
            int updatedCount = 0;

            try
            {
                // Process conditions
                if (conditionGroupBuilder.Conditions != null)
                {
                    foreach (var condition in conditionGroupBuilder.Conditions)
                    {
                        if (condition is ConditionItem.Builder conditionItemBuilder && conditionItemBuilder.Actions != null)
                        {
                            // ConditionItem.Builder also has Actions property (ActionScope.Builder)
                            updatedCount += ProcessActionsForEntityReplacement(conditionItemBuilder.Actions, componentName);
                        }
                    }
                }

                // Process else actions
                if (conditionGroupBuilder.ElseActions != null)
                {
                    foreach (var elseAction in conditionGroupBuilder.ElseActions)
                    {
                        if (elseAction is Question.Builder questionBuilder)
                        {
                            updatedCount += ProcessQuestionForEntityReplacement(questionBuilder, componentName);
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"[ProjectConverter] Error processing condition group in {componentName}: {ex.Message}");
            }

            return updatedCount;
        }

        /// <summary>
        /// Processes a single Question to check if it has a replaceable entity and update it with a better entity if available
        /// Skips questions with ZipCode, PhoneNumber, Number, and Money prebuilt entities
        /// </summary>
        /// <param name="questionBuilder">The question to process</param>
        /// <param name="componentName">Name of the component for logging</param>
        /// <returns>1 if the question was updated, 0 otherwise</returns>
        private int ProcessQuestionForEntityReplacement(Question.Builder questionBuilder, string componentName)
        {
            try
            {
                // Check if this question has a replaceable entity (NOT one of the 4 excluded types)
                if (!(questionBuilder.Entity is ZipCodePrebuiltEntity.Builder) &&
                    !(questionBuilder.Entity is PhoneNumberPrebuiltEntity.Builder) &&
                    !(questionBuilder.Entity is NumberPrebuiltEntity.Builder) &&
                    !(questionBuilder.Entity is MoneyPrebuiltEntity.Builder))
                {
                    string questionId = questionBuilder.Id.ToString();
                    string entityType = questionBuilder.Entity?.GetType().Name ?? "null";
                    Console.WriteLine($"[ProjectConverter] Found question '{questionId}' with replaceable entity '{entityType}' in component '{componentName}'");

                    // Try to find a better entity from external grammar mappings
                    string betterEntityName = FindBetterEntityForQuestion(questionId, componentName);

                    if (!string.IsNullOrEmpty(betterEntityName))
                    {
                        // Look up the actual entity from our external grammar entity map
                        if (_externalGrammarToEntityMap.ContainsKey(betterEntityName))
                        {
                            var betterEntity = _externalGrammarToEntityMap[betterEntityName];

                            Console.WriteLine($"[ProjectConverter] ✓ Found better entity '{betterEntityName}' for question '{questionId}' in component '{componentName}'");
                            Console.WriteLine($"[ProjectConverter] Entity type: {betterEntity?.GetType().Name ?? "null"}");

                            if (betterEntity != null)
                            {
                                // Create a new entity using the same pattern as ComponentConverter
                                // This follows the logic you showed: creating ClosedListEntity with items from action list
                                var newEntity = CreateEntityFromExternalGrammarEntity(betterEntity, betterEntityName);

                                if (newEntity != null)
                                {
                                    questionBuilder.Entity = newEntity;
                                    Console.WriteLine($"[ProjectConverter] ✓ Successfully updated question '{questionId}' from '{entityType}' to '{betterEntityName}' in component '{componentName}'");
                                    return 1;
                                }
                                else
                                {
                                    Console.WriteLine($"[ProjectConverter] ⚠ Could not create entity from '{betterEntityName}' for question '{questionId}'");
                                }
                            }
                            else
                            {
                                Console.WriteLine($"[ProjectConverter] ⚠ Better entity '{betterEntityName}' is null for question '{questionId}'");
                            }
                        }
                        else
                        {
                            Console.WriteLine($"[ProjectConverter] ⚠ Better entity '{betterEntityName}' found for question '{questionId}' but not available in external grammar map");
                        }
                    }
                    else
                    {
                        Console.WriteLine($"[ProjectConverter] No better entity found for question '{questionId}' in component '{componentName}'");
                    }
                }
                else
                {
                    // This question has one of the excluded entity types, skip it
                    string questionId = questionBuilder.Id.ToString();
                    string entityType = questionBuilder.Entity?.GetType().Name ?? "null";
                    Console.WriteLine($"[ProjectConverter] Skipping question '{questionId}' with protected entity type '{entityType}' in component '{componentName}'");
                }
            }
            catch (Exception ex)
            {
                string questionId = questionBuilder.Id.ToString();
                Console.WriteLine($"[ProjectConverter] Error processing question '{questionId}' in component '{componentName}': {ex.Message}");
            }

            return 0;
        }

        /// <summary>
        /// Finds a better entity name for a question based on grammar mappings
        /// </summary>
        /// <param name="questionId">The ID of the question</param>
        /// <param name="componentName">Name of the component for context</param>
        /// <returns>The name of a better entity if found, null otherwise</returns>
        private string FindBetterEntityForQuestion(string questionId, string componentName)
        {
            // Strategy 1: Direct lookup by question ID
            if (_allGrammarNameToEntityMappings.ContainsKey(questionId))
            {
                string entityName = _allGrammarNameToEntityMappings[questionId];
                Console.WriteLine($"[ProjectConverter] Found direct grammar mapping: {questionId} -> {entityName}");
                return entityName;
            }

            // Strategy 2: Look for grammar mappings that might be related to this question
            // This could include variations of the question ID or related grammar names
            foreach (var grammarMapping in _allGrammarNameToEntityMappings)
            {
                // Check if the grammar name contains the question ID or vice versa
                if (grammarMapping.Key.Contains(questionId) || questionId.Contains(grammarMapping.Key))
                {
                    Console.WriteLine($"[ProjectConverter] Found related grammar mapping: {grammarMapping.Key} -> {grammarMapping.Value} for question {questionId}");
                    return grammarMapping.Value;
                }
            }

            // Strategy 3: Could add more sophisticated matching logic here
            // For example, fuzzy matching, pattern matching, etc.

            Console.WriteLine($"[ProjectConverter] No grammar mapping found for question '{questionId}' in component '{componentName}'");
            return null;
        }

        /// <summary>
        /// Creates a Bot Framework entity from a CopilotStudioEntity using the same pattern as ComponentConverter
        /// This follows the logic: creating ClosedListEntity with items from action list
        /// </summary>
        /// <param name="copilotStudioEntity">The CopilotStudioEntity from external grammar mapping</param>
        /// <param name="entityName">The name of the entity for logging</param>
        /// <returns>An EmbeddedEntity.Builder or null if creation fails</returns>
        private EmbeddedEntity.Builder CreateEntityFromExternalGrammarEntity(CopilotStudioEntity copilotStudioEntity, string entityName)
        {
            try
            {
                if (copilotStudioEntity == null)
                {
                    Console.WriteLine($"[ProjectConverter] CopilotStudioEntity '{entityName}' is null, cannot create entity");
                    return null;
                }

                Console.WriteLine($"[ProjectConverter] Creating entity from CopilotStudioEntity '{entityName}' of type: {copilotStudioEntity.GetType().Name}");

                // Create a ClosedListEntity following the same pattern as ComponentConverter
                var closedListEntityBuilder = new ClosedListEntity.Builder();

                // Try to extract items from the CopilotStudioEntity
                // This will depend on the actual structure of your CopilotStudioEntity
                var entityItems = ExtractItemsFromCopilotStudioEntity(copilotStudioEntity, entityName);

                if (entityItems != null && entityItems.Count > 0)
                {
                    // Follow the same pattern as ComponentConverter:
                    // Group by name and select first, then add as ClosedListItem
                    var uniqueItems = entityItems.GroupBy(item => item).Select(group => group.First()).ToList();

                    foreach (var item in uniqueItems)
                    {
                        // Add each item as both Id and DisplayName in ClosedListItem
                        // This mirrors the ComponentConverter logic you showed
                        closedListEntityBuilder.Items.Add(new ClosedListItem.Builder()
                        {
                            Id = item,
                            DisplayName = item
                        });
                    }

                    // Create the EmbeddedEntity with the ClosedListEntity definition
                    var embeddedEntity = new EmbeddedEntity.Builder()
                    {
                        Definition = closedListEntityBuilder
                    };

                    // Set DTMF options similar to ComponentConverter
                    closedListEntityBuilder.DtmfMultipleChoiceOptions = new DtmfMultipleChoiceOptions.Builder()
                    {
                        GenerateMapping = false,
                        ReadOutOptions = false
                    };

                    Console.WriteLine($"[ProjectConverter] Successfully created entity '{entityName}' with {uniqueItems.Count} items");
                    return embeddedEntity;
                }
                else
                {
                    Console.WriteLine($"[ProjectConverter] No items found in CopilotStudioEntity '{entityName}', cannot create entity");
                    return null;
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"[ProjectConverter] Error creating entity from CopilotStudioEntity '{entityName}': {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// Extracts items from a CopilotStudioEntity to create ClosedListItems
        /// This method needs to be adapted based on the actual structure of your CopilotStudioEntity
        /// </summary>
        /// <param name="copilotStudioEntity">The CopilotStudioEntity to extract items from</param>
        /// <param name="entityName">The name of the entity for logging</param>
        /// <returns>List of item names/values or null if extraction fails</returns>
        private List<string> ExtractItemsFromCopilotStudioEntity(CopilotStudioEntity copilotStudioEntity, string entityName)
        {
            try
            {
                var items = new List<string>();

                // Log the type to help understand the structure
                Console.WriteLine($"[ProjectConverter] Extracting items from CopilotStudioEntity '{entityName}' of type: {copilotStudioEntity.GetType().Name}");

                // Since we can't directly access the CopilotStudioEntity properties due to type constraints,
                // we'll use reflection to try to extract common properties that might contain the items

                var entityType = copilotStudioEntity.GetType();

                // Try to find properties that might contain the entity items
                // Common property names that might exist: Items, Values, Entries, Options, etc.
                var possibleItemProperties = new[] { "Items", "Values", "Entries", "Options", "Choices", "List" };

                foreach (var propertyName in possibleItemProperties)
                {
                    var property = entityType.GetProperty(propertyName);
                    if (property != null)
                    {
                        Console.WriteLine($"[ProjectConverter] Found property '{propertyName}' in CopilotStudioEntity '{entityName}'");

                        var propertyValue = property.GetValue(copilotStudioEntity);
                        if (propertyValue != null)
                        {
                            // Try to extract items from the property value
                            var extractedItems = ExtractItemsFromProperty(propertyValue, propertyName, entityName);
                            if (extractedItems != null && extractedItems.Count > 0)
                            {
                                items.AddRange(extractedItems);
                                Console.WriteLine($"[ProjectConverter] Extracted {extractedItems.Count} items from property '{propertyName}' in entity '{entityName}'");
                            }
                        }
                    }
                }

                // If no items found through reflection, try to use the entity name as a fallback
                if (items.Count == 0)
                {
                    Console.WriteLine($"[ProjectConverter] No items found through reflection for entity '{entityName}', using entity name as fallback");
                    items.Add(entityName);
                }

                Console.WriteLine($"[ProjectConverter] Total items extracted from CopilotStudioEntity '{entityName}': {items.Count}");
                return items;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"[ProjectConverter] Error extracting items from CopilotStudioEntity '{entityName}': {ex.Message}");

                // Fallback: return the entity name as a single item
                Console.WriteLine($"[ProjectConverter] Using fallback: entity name '{entityName}' as single item");
                return new List<string> { entityName };
            }
        }

        /// <summary>
        /// Helper method to extract items from a property value using reflection
        /// </summary>
        /// <param name="propertyValue">The property value to extract items from</param>
        /// <param name="propertyName">The name of the property for logging</param>
        /// <param name="entityName">The entity name for logging</param>
        /// <returns>List of extracted item strings</returns>
        private List<string> ExtractItemsFromProperty(object propertyValue, string propertyName, string entityName)
        {
            var items = new List<string>();

            try
            {
                // Check if the property value is a collection
                if (propertyValue is System.Collections.IEnumerable enumerable && !(propertyValue is string))
                {
                    foreach (var item in enumerable)
                    {
                        if (item != null)
                        {
                            // Try to get a meaningful string representation
                            string itemString = ExtractStringFromItem(item);
                            if (!string.IsNullOrEmpty(itemString))
                            {
                                items.Add(itemString);
                            }
                        }
                    }
                }
                else if (propertyValue is string stringValue)
                {
                    // If it's a single string value
                    items.Add(stringValue);
                }
                else
                {
                    // Try to convert to string
                    string propertyStringValue = propertyValue.ToString() ?? "";
                    if (!string.IsNullOrEmpty(propertyStringValue) && propertyStringValue != propertyValue.GetType().Name)
                    {
                        items.Add(propertyStringValue);
                    }
                }

                Console.WriteLine($"[ProjectConverter] Extracted {items.Count} items from property '{propertyName}' in entity '{entityName}'");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"[ProjectConverter] Error extracting items from property '{propertyName}' in entity '{entityName}': {ex.Message}");
            }

            return items;
        }

        /// <summary>
        /// Helper method to extract a meaningful string from an item object
        /// </summary>
        /// <param name="item">The item object to extract string from</param>
        /// <returns>String representation of the item</returns>
        private string ExtractStringFromItem(object item)
        {
            try
            {
                // Try common properties that might contain the item value
                var itemType = item.GetType();
                var possibleValueProperties = new[] { "Name", "Value", "Id", "DisplayName", "Text", "Label" };

                foreach (var propName in possibleValueProperties)
                {
                    var prop = itemType.GetProperty(propName);
                    if (prop != null)
                    {
                        var value = prop.GetValue(item);
                        if (value is string stringValue && !string.IsNullOrEmpty(stringValue))
                        {
                            return stringValue;
                        }
                    }
                }

                // Fallback to ToString()
                string fallbackValue = item.ToString();
                return fallbackValue != itemType.Name ? fallbackValue : null;
            }
            catch
            {
                return item.ToString();
            }
        }



        /// <summary>
        /// Processes a dialog component to find and modify question entities
        /// </summary>
        /// <param name="dialogComponentBuilder">The dialog component builder to process</param>
        private void ProcessDialogComponentEntities(DialogComponent.Builder dialogComponentBuilder)
        {
            // This method would need to traverse the dialog structure to find questions
            // and modify their entities as needed
            // Implementation depends on your specific requirements for when to set StringPrebuiltEntity

            Console.WriteLine($"[ProjectConverter] Post-processing entities for dialog component: {dialogComponentBuilder.DisplayName}");

            // Example: You could add logic here to find specific questions and modify their entities
            // This is a placeholder - you'll need to implement the actual logic based on your needs

            // To actually modify question entities, you would need to:
            // 1. Access the dialog structure (dialogComponentBuilder.Dialog)
            // 2. Traverse through actions to find Question actions
            // 3. Modify their Entity property to set new StringPrebuiltEntity()

            // Example implementation (you'll need to adapt this to your specific needs):
            // if (dialogComponentBuilder.Dialog != null)
            // {
            //     // Traverse the dialog structure to find and modify questions
            //     // This would require understanding the specific dialog structure in your case
            // }
        }


        public Language ConvertLanguageCodeToBotEntityLanguage(string languageCode)
        {
            switch (languageCode)
            {
                case "en-US":
                    return Language.English;
                case "fr-FR":
                    return Language.French;
                case "es-ES":
                    return Language.Spanish;
                case "ar-WW":
                case "arb-146":
                    return Language.Arabic;
                case "zh-CN":
                    return Language.Chinese_Simplified;
                case "zh-TW":
                    return Language.Chinese_Traditional;
                case "cs-CZ":
                    return Language.Czech;
                case "da-DK":
                    return Language.Danish;
                case "nl-NL":
                    return Language.Dutch;
                case "fi-FI":
                    return Language.Finnish;
                case "de-DE":
                    return Language.German;
                case "el-GR":
                    return Language.Greek;
                case "hi-IN":
                    return Language.Hindi;
                case "id-ID":
                    return Language.Indonesian;
                case "it-IT":
                    return Language.Italian;
                case "ja-JP":
                    return Language.Japanese;
                case "ko-KR":
                    return Language.Korean;
                case "nb-NO":
                    return Language.Norwegian;
                case "pl-PL":
                    return Language.Polish;
                case "pt-BR":
                    return Language.Portuguese_Brazilian;
                case "ru-RU":
                    return Language.Russian;
                case "th-TH":
                    return Language.Thai;
                case "sv-SE":
                    return Language.Swedish;
                case "tr-TR":
                    return Language.Turkish;
                case "en-GB":
                    return Language.English_UK;
                case "fr-CA":
                    return Language.French_Canada;
                case "es-US":
                    return Language.Spanish_US;
                case "en-AU":
                    return Language.English;//Language.English_AU; TODO: Add English_AU with a later ObjectModel.
                default:
                    return 0; // unknown
            }
        }
        public void ConvertComponents()
        {
            foreach (var component in _project.dialogModels)
            {
                // Add your implementation here
                // Do something with each component
            }
        }
        private static Dictionary<string, CopilotStudioEntity> ConvertExternalGrammarJsonToMap(DialogList ndfProject, Dictionary<string, string> collectedGrammarMappings)
        {
            Dictionary<string, CopilotStudioEntity> externalGrammarToEntityMap = new Dictionary<string, CopilotStudioEntity>();
            try
            {
                Console.WriteLine($"Attempting to load external grammar JSON from: {ExternalGrammarJsonPath}");
                Console.WriteLine($"File exists: {System.IO.File.Exists(ExternalGrammarJsonPath)}");

                // Use the collected grammar mappings instead of extracting them again
                var grammarNameToEntityMap = collectedGrammarMappings;
                Console.WriteLine($"Using collected grammar name to entity map count: {grammarNameToEntityMap?.Count ?? 0}");

                // Only call ExternalGrammarConverter if we have grammar mappings
                if (grammarNameToEntityMap != null && grammarNameToEntityMap.Count > 0)
                {
                    try
                    {
                        // Check if the JSON file exists and is valid before calling the external converter
                        if (!System.IO.File.Exists(ExternalGrammarJsonPath))
                        {
                            Console.WriteLine($"External grammar JSON file not found: {ExternalGrammarJsonPath}");
                            return externalGrammarToEntityMap;
                        }

                        // Validate the JSON file is not empty
                        var jsonContent = System.IO.File.ReadAllText(ExternalGrammarJsonPath);
                        if (string.IsNullOrWhiteSpace(jsonContent))
                        {
                            Console.WriteLine("External grammar JSON file is empty");
                            return externalGrammarToEntityMap;
                        }

                        Console.WriteLine($"Calling ExternalGrammarConverter with {grammarNameToEntityMap.Count} grammar mappings");

                        // Validate and filter the grammar mappings to prevent null reference exceptions
                        var validatedGrammarMap = ValidateGrammarMappings(grammarNameToEntityMap, ExternalGrammarJsonPath);
                        Console.WriteLine($"Validated grammar mappings: {validatedGrammarMap.Count} valid out of {grammarNameToEntityMap.Count} total");

                        if (validatedGrammarMap.Count > 0)
                        {
                            externalGrammarToEntityMap = ExternalGrammarConverter.ConvertJsonPathToEntities(ExternalGrammarJsonPath, validatedGrammarMap);
                            Console.WriteLine($"Successfully converted external grammar JSON. Entity count: {externalGrammarToEntityMap?.Count ?? 0}");
                        }
                        else
                        {
                            Console.WriteLine("No valid grammar mappings found after validation. Skipping external grammar conversion.");
                        }
                    }
                    catch (Exception externalEx)
                    {
                        Console.WriteLine($"Error calling ExternalGrammarConverter: {externalEx.Message}");
                        Console.WriteLine($"Stack trace: {externalEx.StackTrace}");
                        Console.WriteLine("Continuing without external grammar conversion...");
                        // Continue without external grammar conversion - this is not a fatal error
                    }
                }
                else
                {
                    Console.WriteLine("No grammar mappings found in the project. Skipping external grammar JSON conversion.");
                }
            }
            catch (Exception ex)
            {
                Console.Out.WriteLine($"Error parsing external grammar json: {ex.Message}");
                Console.Out.WriteLine($"Stack trace: {ex.StackTrace}");
                if (ex.InnerException != null)
                {
                    Console.Out.WriteLine($"Inner exception: {ex.InnerException.Message}");
                }
            }

            return externalGrammarToEntityMap;
        }

        /// <summary>
        /// Validates grammar mappings against the external grammar JSON to prevent null reference exceptions
        /// Filters out mappings that don't have corresponding valid entries in the JSON
        /// </summary>
        /// <param name="grammarNameToEntityMap">Original grammar to entity mappings</param>
        /// <param name="externalGrammarJsonPath">Path to the external grammar JSON file</param>
        /// <returns>Validated and filtered grammar mappings</returns>
        private static Dictionary<string, string> ValidateGrammarMappings(Dictionary<string, string> grammarNameToEntityMap, string externalGrammarJsonPath)
        {
            var validatedMap = new Dictionary<string, string>();

            try
            {
                // Read and parse the external grammar JSON
                var jsonContent = System.IO.File.ReadAllText(externalGrammarJsonPath);
                var externalGrammarMappings = System.Text.Json.JsonSerializer.Deserialize<List<ExternalGrammarEntityMapping>>(jsonContent);

                if (externalGrammarMappings == null)
                {
                    Console.WriteLine("Failed to parse external grammar JSON - null result");
                    return validatedMap;
                }

                Console.WriteLine($"Loaded {externalGrammarMappings.Count} external grammar mappings from JSON");

                // Create a set of valid entity names from the JSON
                // Only include entities that have all required fields populated
                var validEntityNames = new HashSet<string>();
                foreach (var mapping in externalGrammarMappings)
                {
                    if (IsValidExternalGrammarMapping(mapping))
                    {
                        validEntityNames.Add(mapping.Entity);
                    }
                }

                Console.WriteLine($"Found {validEntityNames.Count} valid entity names in external grammar JSON");

                // Filter the grammar mappings to only include those with valid entities
                foreach (var kvp in grammarNameToEntityMap)
                {
                    if (!string.IsNullOrWhiteSpace(kvp.Key) && !string.IsNullOrWhiteSpace(kvp.Value))
                    {
                        if (validEntityNames.Contains(kvp.Value))
                        {
                            validatedMap[kvp.Key] = kvp.Value;
                        }
                        else
                        {
                            Console.WriteLine($"Skipping grammar mapping '{kvp.Key}' -> '{kvp.Value}' - entity not found in external grammar JSON");
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error validating grammar mappings: {ex.Message}");
                Console.WriteLine("Returning empty validated map to prevent external converter errors");
            }

            return validatedMap;
        }

        /// <summary>
        /// Validates that an external grammar mapping has all required fields populated
        /// to prevent null reference exceptions in ExternalGrammarConverter
        /// </summary>
        /// <param name="mapping">The external grammar mapping to validate</param>
        /// <returns>True if the mapping is valid and safe to use</returns>
        private static bool IsValidExternalGrammarMapping(ExternalGrammarEntityMapping mapping)
        {
            if (mapping == null)
                return false;

            // Entity name must be present and non-empty
            if (string.IsNullOrWhiteSpace(mapping.Entity))
                return false;

            // Grammars list must exist and have at least one entry
            if (mapping.Grammars == null || mapping.Grammars.Count == 0)
                return false;

            // Check that all grammar entries are non-empty
            foreach (var grammar in mapping.Grammars)
            {
                if (string.IsNullOrWhiteSpace(grammar))
                    return false;
            }

            // EntityType should be present (though it might be null in some cases)
            // We'll be lenient here and not require it

            // SWI_meaning can be null for some entity types, so we won't require it

            return true;
        }
    }

    /// <summary>
    /// Represents the structure of external grammar entity mapping from JSON
    /// Used for validation before calling ExternalGrammarConverter
    /// </summary>
    public class ExternalGrammarEntityMapping
    {
        public string? Entity { get; set; }
        public List<string>? Grammars { get; set; }
        public string? EntityType { get; set; }
        public string? Reason { get; set; }
        public bool GrxmlValidation { get; set; }
        // Add other properties as needed based on the JSON structure
    }
}
